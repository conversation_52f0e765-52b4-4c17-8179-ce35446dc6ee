package test

import (
	"net/http"
	"testing"
)

func TestLunchBoxAPI(t *testing.T) {
	e := tester(t)

	// Test getting lunch box list (should fail without authentication)
	t.Run("GetLunchBoxList", func(t *testing.T) {
		e.GET("/api/v2/lunch-boxes").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting lunch box list with query parameters
	t.Run("GetLunchBoxListWithParams", func(t *testing.T) {
		e.GET("/api/v2/lunch-boxes").
			WithQuery("category_id", 1).
			WithQuery("search", "测试餐盒").
			WithQuery("state", 1).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test creating lunch box (should fail without authentication)
	t.Run("CreateLunchBox", func(t *testing.T) {
		e.POST("/api/v2/lunch-boxes").
			WithJSON(map[string]interface{}{
				"food_category_id":   1,
				"image":              "/images/lunchbox1.jpg",
				"shortcut_code":      "L001",
				"name_ug":            "تاماق قۇتىسى",
				"name_zh":            "测试餐盒",
				"cost_price":         15.00,
				"vip_price":          14.00,
				"price":              16.00,
				"format_id":          1,
				"is_special_food":    false,
				"support_scan_order": true,
				"sort":               1,
				"state":              1,
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test creating lunch box with invalid data
	t.Run("CreateLunchBoxInvalidData", func(t *testing.T) {
		e.POST("/api/v2/lunch-boxes").
			WithJSON(map[string]interface{}{
				"food_category_id": 0, // Invalid category ID
				"name_ug":          "",
				"name_zh":          "",
				"cost_price":       -1, // Invalid price
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test updating lunch box (should fail without authentication)
	t.Run("UpdateLunchBox", func(t *testing.T) {
		e.PUT("/api/v2/lunch-boxes/1").
			WithJSON(map[string]interface{}{
				"food_category_id":   1,
				"image":              "/images/lunchbox1_updated.jpg",
				"shortcut_code":      "L001",
				"name_ug":            "يېڭىلانغان تاماق قۇتىسى",
				"name_zh":            "更新的餐盒",
				"cost_price":         16.00,
				"vip_price":          15.00,
				"price":              17.00,
				"format_id":          1,
				"is_special_food":    true,
				"support_scan_order": true,
				"sort":               2,
				"state":              1,
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test updating lunch box with invalid ID
	t.Run("UpdateLunchBoxInvalidID", func(t *testing.T) {
		e.PUT("/api/v2/lunch-boxes/invalid").
			WithJSON(map[string]interface{}{
				"food_category_id": 1,
				"name_ug":          "تاماق قۇتىسى",
				"name_zh":          "餐盒",
				"cost_price":       15.00,
				"vip_price":        14.00,
				"price":            16.00,
				"format_id":        1,
				"sort":             1,
				"state":            1,
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test saving lunch box sort (should fail without authentication)
	t.Run("SaveLunchBoxSort", func(t *testing.T) {
		e.POST("/api/v2/lunch-boxes/sort").
			WithJSON(map[string]interface{}{
				"lunch_boxes": []map[string]interface{}{
					{"id": 1, "sort": 1},
					{"id": 2, "sort": 2},
					{"id": 3, "sort": 3},
				},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test saving lunch box sort with invalid data
	t.Run("SaveLunchBoxSortInvalidData", func(t *testing.T) {
		e.POST("/api/v2/lunch-boxes/sort").
			WithJSON(map[string]interface{}{
				"lunch_boxes": []map[string]interface{}{
					{"id": 0, "sort": -1}, // Invalid ID and sort
				},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test deleting lunch box (should fail without authentication)
	t.Run("DeleteLunchBox", func(t *testing.T) {
		e.DELETE("/api/v2/lunch-boxes/1").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test deleting lunch box with invalid ID
	t.Run("DeleteLunchBoxInvalidID", func(t *testing.T) {
		e.DELETE("/api/v2/lunch-boxes/invalid").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting lunch box associated foods (should fail without authentication)
	t.Run("GetLunchBoxAssociatedFoods", func(t *testing.T) {
		e.GET("/api/v2/lunch-boxes/1/foods").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting lunch box associated foods with pagination
	t.Run("GetLunchBoxAssociatedFoodsWithPagination", func(t *testing.T) {
		e.GET("/api/v2/lunch-boxes/1/foods").
			WithQuery("page", 1).
			WithQuery("limit", 5).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test associating foods (should fail without authentication)
	t.Run("AssociateFoods", func(t *testing.T) {
		e.POST("/api/v2/lunch-boxes/foods").
			WithJSON(map[string]interface{}{
				"lunch_box_id": 1,
				"category_id":  1,
				"foods": []map[string]interface{}{
					{"food_id": 1, "price": 0},   // 不加价
					{"food_id": 2, "price": 100}, // 加价1元（以分为单位）
					{"food_id": 3, "price": 50},  // 加价0.5元
				},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test associating foods with invalid data
	t.Run("AssociateFoodsInvalidData", func(t *testing.T) {
		e.POST("/api/v2/lunch-boxes/foods").
			WithJSON(map[string]interface{}{
				"lunch_box_id": 0, // Invalid lunch box ID
				"category_id":  0, // Invalid category ID
				"foods":        []map[string]interface{}{},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test disassociating foods (should fail without authentication)
	t.Run("DisassociateFoods", func(t *testing.T) {
		e.DELETE("/api/v2/lunch-boxes/foods").
			WithJSON(map[string]interface{}{
				"lunch_box_id": 1,
				"food_ids":     []int64{1, 2, 3},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test disassociating foods with invalid data
	t.Run("DisassociateFoodsInvalidData", func(t *testing.T) {
		e.DELETE("/api/v2/lunch-boxes/foods").
			WithJSON(map[string]interface{}{
				"lunch_box_id": 0,         // Invalid lunch box ID
				"food_ids":     []int64{}, // Empty food IDs
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test edge cases
	t.Run("EdgeCases", func(t *testing.T) {
		// Test with very large page number
		e.GET("/api/v2/lunch-boxes").
			WithQuery("page", 999999).
			WithQuery("limit", 100).
			Expect().
			Status(http.StatusUnauthorized)

		// Test with zero limit
		e.GET("/api/v2/lunch-boxes").
			WithQuery("page", 1).
			WithQuery("limit", 0).
			Expect().
			Status(http.StatusUnauthorized)

		// Test with negative values
		e.GET("/api/v2/lunch-boxes").
			WithQuery("page", -1).
			WithQuery("limit", -10).
			Expect().
			Status(http.StatusUnauthorized)
	})

	// Test business scenarios
	t.Run("BusinessScenarios", func(t *testing.T) {
		// Test creating lunch box with special characters in names
		e.POST("/api/v2/lunch-boxes").
			WithJSON(map[string]interface{}{
				"food_category_id": 1,
				"name_ug":          "ئالاھىدە تاماق قۇتىسى @#$%",
				"name_zh":          "特殊字符餐盒 @#$%",
				"cost_price":       15.00,
				"vip_price":        14.00,
				"price":            16.00,
				"format_id":        1,
				"sort":             1,
				"state":            1,
			}).
			Expect().
			Status(http.StatusUnauthorized)

		// Test creating lunch box with very long names
		longName := "很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的餐盒名称"
		e.POST("/api/v2/lunch-boxes").
			WithJSON(map[string]interface{}{
				"food_category_id": 1,
				"name_ug":          longName,
				"name_zh":          longName,
				"cost_price":       15.00,
				"vip_price":        14.00,
				"price":            16.00,
				"format_id":        1,
				"sort":             1,
				"state":            1,
			}).
			Expect().
			Status(http.StatusUnauthorized)

		// Test batch sort with many items
		manyItems := make([]map[string]interface{}, 100)
		for i := 0; i < 100; i++ {
			manyItems[i] = map[string]interface{}{
				"id":   i + 1,
				"sort": i + 1,
			}
		}
		e.POST("/api/v2/lunch-boxes/sort").
			WithJSON(map[string]interface{}{
				"lunch_boxes": manyItems,
			}).
			Expect().
			Status(http.StatusUnauthorized)

		// Test associating many foods
		manyFoods := make([]map[string]interface{}, 50)
		for i := 0; i < 50; i++ {
			manyFoods[i] = map[string]interface{}{
				"food_id": i + 1,
				"price":   i * 10, // Different prices
			}
		}
		e.POST("/api/v2/lunch-boxes/foods").
			WithJSON(map[string]interface{}{
				"lunch_box_id": 1,
				"category_id":  1,
				"foods":        manyFoods,
			}).
			Expect().
			Status(http.StatusUnauthorized)
	})
}
