package test

import (
	"net/http"
	"testing"
)

func TestMethodAPI(t *testing.T) {
	e := tester(t)

	// Test getting method list (should work without authentication for now)
	t.Run("GetMethodList", func(t *testing.T) {
		e.GET("/api/v2/methods").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test creating method (should fail without authentication)
	t.Run("CreateMethod", func(t *testing.T) {
		e.POST("/api/v2/methods").
			WithJSON(map[string]interface{}{
				"group_id": 1,
				"name_zh":  "红烧",
				"name_ug":  "قىزىل پىشۇرۇش",
				"desc":     "红烧做法",
				"price":    500, // 5元，以分为单位
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test updating method (should fail without authentication)
	t.Run("UpdateMethod", func(t *testing.T) {
		e.PUT("/api/v2/methods/1").
			WithJSON(map[string]interface{}{
				"group_id": 1,
				"name_zh":  "清蒸",
				"name_ug":  "بۇغلاش",
				"desc":     "清蒸做法",
				"price":    300, // 3元，以分为单位
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test deleting method (should fail without authentication)
	t.Run("DeleteMethod", func(t *testing.T) {
		e.DELETE("/api/v2/methods/1").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test saving method sort (should fail without authentication)
	t.Run("SaveMethodSort", func(t *testing.T) {
		e.POST("/api/v2/methods/sort").
			WithJSON(map[string]interface{}{
				"items": []map[string]interface{}{
					{"id": 1, "sort": 1},
					{"id": 2, "sort": 2},
				},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting method foods (should fail without authentication)
	t.Run("GetMethodFoods", func(t *testing.T) {
		e.GET("/api/v2/methods/foods").
			WithQuery("method_id", 1).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test saving food method association (should fail without authentication)
	t.Run("SaveFoodMethod", func(t *testing.T) {
		e.POST("/api/v2/methods/foods").
			WithJSON(map[string]interface{}{
				"method_id": 1,
				"foods": []map[string]interface{}{
					{"food_id": 1, "price": 0},   // 使用默认价格，指定分组
					{"food_id": 2, "price": 200}, // 加价2元，不指定分组
					{"food_id": 3, "price": 0},   // 使用默认价格，不指定分组
				},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})
}
