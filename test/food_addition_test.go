package test

import (
	"net/http"
	"testing"
)

func TestFoodAdditionAPI(t *testing.T) {
	e := tester(t)

	// Test getting food addition list (should fail without authentication)
	t.Run("GetFoodAdditionList", func(t *testing.T) {
		e.GET("/api/v2/food-additions").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting food addition list with query parameters
	t.Run("GetFoodAdditionListWithParams", func(t *testing.T) {
		e.GET("/api/v2/food-additions").
			WithQuery("page", 1).
			WithQuery("limit", 10).
			WithQuery("category_id", 1).
			WithQuery("keyword", "测试").
			WithQuery("state", 1).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test creating food addition (should fail without authentication)
	t.<PERSON>("CreateFoodAddition", func(t *testing.T) {
		e.POST("/api/v2/food-additions").
			WithJSON(map[string]interface{}{
				"food_category_id":   1,
				"image":              "/images/addition1.jpg",
				"shortcut_code":      "A001",
				"name_ug":            "قوشۇمچە مادداسى",
				"name_zh":            "测试加料",
				"cost_price":         5.00,
				"vip_price":          4.50,
				"price":              5.50,
				"format_id":          1,
				"is_special_food":    false,
				"support_scan_order": true,
				"sort":               1,
				"state":              1,
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test creating food addition with invalid data
	t.Run("CreateFoodAdditionInvalidData", func(t *testing.T) {
		e.POST("/api/v2/food-additions").
			WithJSON(map[string]interface{}{
				"food_category_id": 0, // Invalid category ID
				"name_ug":          "",
				"name_zh":          "",
				"cost_price":       -1, // Invalid price
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test updating food addition (should fail without authentication)
	t.Run("UpdateFoodAddition", func(t *testing.T) {
		e.PUT("/api/v2/food-additions/1").
			WithJSON(map[string]interface{}{
				"food_category_id":   1,
				"image":              "/images/addition1_updated.jpg",
				"shortcut_code":      "A001",
				"name_ug":            "يېڭىلانغان قوشۇمچە",
				"name_zh":            "更新的加料",
				"cost_price":         6.00,
				"vip_price":          5.50,
				"price":              6.50,
				"format_id":          1,
				"is_special_food":    true,
				"support_scan_order": true,
				"sort":               2,
				"state":              1,
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test updating food addition with invalid ID
	t.Run("UpdateFoodAdditionInvalidID", func(t *testing.T) {
		e.PUT("/api/v2/food-additions/invalid").
			WithJSON(map[string]interface{}{
				"food_category_id": 1,
				"name_ug":          "قوشۇمچە",
				"name_zh":          "加料",
				"cost_price":       5.00,
				"vip_price":        4.50,
				"price":            5.50,
				"format_id":        1,
				"sort":             1,
				"state":            1,
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test deleting food addition (should fail without authentication)
	t.Run("DeleteFoodAddition", func(t *testing.T) {
		e.DELETE("/api/v2/food-additions/1").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test deleting food addition with invalid ID
	t.Run("DeleteFoodAdditionInvalidID", func(t *testing.T) {
		e.DELETE("/api/v2/food-additions/invalid").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting addition associated foods (should fail without authentication)
	t.Run("GetAdditionFoods", func(t *testing.T) {
		e.GET("/api/v2/food-additions/1/foods").
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test getting addition associated foods with pagination
	t.Run("GetAdditionAssociatedFoodsWithPagination", func(t *testing.T) {
		e.GET("/api/v2/food-additions/1/foods").
			WithQuery("page", 1).
			WithQuery("limit", 5).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test associating foods (should fail without authentication)
	t.Run("SaveFoodAdditionFoods", func(t *testing.T) {
		e.POST("/api/v2/food-additions/foods").
			WithJSON(map[string]interface{}{
				"addition_id": 1,
				"category_id": 1,
				"foods": []map[string]interface{}{
					{"food_id": 1, "price": 0},   // 不加价
					{"food_id": 2, "price": 100}, // 加价1元（以分为单位）
					{"food_id": 3, "price": 50},  // 加价0.5元
				},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test associating foods with invalid data
	t.Run("AssociateFoodsInvalidData", func(t *testing.T) {
		e.POST("/api/v2/food-additions/foods").
			WithJSON(map[string]interface{}{
				"addition_id": 0, // Invalid addition ID
				"category_id": 0, // Invalid category ID
				"foods":       []map[string]interface{}{},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test disassociating foods (should fail without authentication)
	t.Run("RemoveFoodAdditionFoods", func(t *testing.T) {
		e.DELETE("/api/v2/food-additions/foods").
			WithJSON(map[string]interface{}{
				"addition_id": 1,
				"food_ids":    []int64{1, 2, 3},
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test disassociating foods with invalid data
	t.Run("DisassociateFoodsInvalidData", func(t *testing.T) {
		e.DELETE("/api/v2/food-additions/foods").
			WithJSON(map[string]interface{}{
				"addition_id": 0,         // Invalid addition ID
				"food_ids":    []int64{}, // Empty food IDs
			}).
			Expect().
			Status(http.StatusUnauthorized) // Expected since we don't have auth token
	})

	// Test edge cases
	t.Run("EdgeCases", func(t *testing.T) {
		// Test with very large page number
		e.GET("/api/v2/food-additions").
			WithQuery("page", 999999).
			WithQuery("limit", 100).
			Expect().
			Status(http.StatusUnauthorized)

		// Test with zero limit
		e.GET("/api/v2/food-additions").
			WithQuery("page", 1).
			WithQuery("limit", 0).
			Expect().
			Status(http.StatusUnauthorized)

		// Test with negative values
		e.GET("/api/v2/food-additions").
			WithQuery("page", -1).
			WithQuery("limit", -10).
			Expect().
			Status(http.StatusUnauthorized)
	})
}
