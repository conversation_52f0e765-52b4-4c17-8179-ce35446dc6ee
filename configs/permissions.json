[{"v0": "permission:merchant-cashier-login", "v1": "", "v2": ""}, {"v0": "permission:merchant-info-management", "v1": "api/v1/clear-password", "v2": "POST"}, {"v0": "permission:area-management", "v1": "api/v1/area/add", "v2": "POST"}, {"v0": "permission:area-management", "v1": "api/v1/area/delete/{area}", "v2": "DELETE"}, {"v0": "permission:area-management", "v1": "api/v1/area/list", "v2": "GET"}, {"v0": "permission:area-management", "v1": "api/v1/area/state/{area}", "v2": "GET"}, {"v0": "permission:area-management", "v1": "api/v1/area/update/{area}", "v2": "PUT"}, {"v0": "permission:bill-management", "v1": "api/v1/bill/total", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/food/add", "v2": "POST"}, {"v0": "permission:food-management", "v1": "api/v1/food/delete/{food}", "v2": "DELETE"}, {"v0": "permission:food-management", "v1": "api/v1/food/list", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/food/state/{food}", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/food/update/{food}", "v2": "PUT"}, {"v0": "permission:food-management", "v1": "api/v1/foodCategories/add", "v2": "POST"}, {"v0": "permission:food-management", "v1": "api/v1/foodCategories/delete/{foodCategory}", "v2": "DELETE"}, {"v0": "permission:food-management", "v1": "api/v1/foodCategories/list", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/foodCategories/state/{foodCategory}", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/foodCategories/update/{foodCategory}", "v2": "PUT"}, {"v0": "permission:food-management", "v1": "api/v1/foods/format", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/foods_default_images", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/images", "v2": "POST"}, {"v0": "permission:vip-management", "v1": "api/v1/level/add", "v2": "POST"}, {"v0": "permission:vip-management", "v1": "api/v1/level/delete/{level}", "v2": "DELETE"}, {"v0": "permission:vip-management", "v1": "api/v1/level/list", "v2": "GET"}, {"v0": "permission:vip-management", "v1": "api/v1/level/update/{level}", "v2": "PUT"}, {"v0": "permission:food-management", "v1": "api/v1/library-foods-category/list", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/library-foods/images", "v2": "GET"}, {"v0": "permission:food-management", "v1": "api/v1/library-foods/import", "v2": "POST"}, {"v0": "permission:food-management", "v1": "api/v1/library-foods/list", "v2": "GET"}, {"v0": "permission:merchant-info-management", "v1": "api/v1/update/merchant", "v2": "PUT"}, {"v0": "permission:merchant-info-management", "v1": "api/v1/merchant_info", "v2": "GET"}, {"v0": "permission:merchant-setting-management", "v1": "api/v1/merchantconfig/list", "v2": "GET"}, {"v0": "permission:merchant-setting-management", "v1": "api/v1/merchantconfig/update", "v2": "POST"}, {"v0": "permission:payment-type-management", "v1": "api/v1/paymentTypes/state/{merchantPaymentType}", "v2": "GET"}, {"v0": "permission:payment-type-management", "v1": "api/v1/paymentTypes/update/{merchantPaymentType}", "v2": "PUT"}, {"v0": "permission:printer-management", "v1": "api/v1/printer", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/cloud-printer-test", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/foods", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/foods", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/foods_all", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/foods_category", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/state/{printer}", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/{printer}", "v2": "PUT"}, {"v0": "permission:printer-management", "v1": "api/v1/printer/{printer}", "v2": "DELETE"}, {"v0": "permission:printer-management", "v1": "api/v2/printers", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}", "v2": "PUT"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}", "v2": "DELETE"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}/status", "v2": "PUT"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/foods", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/foods", "v2": "POST"}, {"v0": "permission:remark-management", "v1": "api/v1/remarkCategory", "v2": "GET"}, {"v0": "permission:remark-management", "v1": "api/v1/remarks/add", "v2": "POST"}, {"v0": "permission:remark-management", "v1": "api/v1/remarks/delete/{remarks}", "v2": "DELETE"}, {"v0": "permission:remark-management", "v1": "api/v1/remarks/list", "v2": "GET"}, {"v0": "permission:remark-management", "v1": "api/v1/remarks/state/{remarks}", "v2": "GET"}, {"v0": "permission:remark-management", "v1": "api/v1/remarks/update/{remarks}", "v2": "PUT"}, {"v0": "permission:waiter-service-management", "v1": "api/v1/service/add", "v2": "POST"}, {"v0": "permission:waiter-service-management", "v1": "api/v1/service/delete", "v2": "POST"}, {"v0": "permission:waiter-service-management", "v1": "api/v1/service/list", "v2": "GET"}, {"v0": "permission:waiter-service-management", "v1": "api/v1/service/update", "v2": "POST"}, {"v0": "permission:shift-management", "v1": "api/v1/shift/delete/{shift}", "v2": "DELETE"}, {"v0": "permission:shift-management", "v1": "api/v1/shift/list", "v2": "GET"}, {"v0": "permission:shift-management", "v1": "api/v1/shift/state/{shift}", "v2": "GET"}, {"v0": "permission:shift-management", "v1": "api/v1/shift/store", "v2": "POST"}, {"v0": "permission:shift-management", "v1": "api/v1/shift/update/{shift}", "v2": "PUT"}, {"v0": "permission:wechat-bind-management", "v1": "api/v1/smart", "v2": "GET"}, {"v0": "permission:wechat-bind-management", "v1": "api/v1/smart-bind-status", "v2": "GET"}, {"v0": "permission:statistic-management", "v1": "api/v2/statistics/business", "v2": "GET"}, {"v0": "permission:statistic-management", "v1": "api/v2/statistics/handover", "v2": "GET"}, {"v0": "permission:statistic-management", "v1": "api/v2/statistics/foods", "v2": "GET"}, {"v0": "permission:statistic-management", "v1": "api/v1/statistics/bill", "v2": "GET"}, {"v0": "permission:statistic-management", "v1": "api/v2/statistics/graph", "v2": "GET"}, {"v0": "permission:table-management", "v1": "api/v1/area/list", "v2": "GET"}, {"v0": "permission:table-management", "v1": "api/v1/table/add", "v2": "POST"}, {"v0": "permission:table-management", "v1": "api/v1/table/delete/{table}", "v2": "DELETE"}, {"v0": "permission:table-management", "v1": "api/v1/table/list", "v2": "GET"}, {"v0": "permission:table-management", "v1": "api/v1/table/qrcode", "v2": "GET"}, {"v0": "permission:table-management", "v1": "api/v1/table/state/{table}", "v2": "GET"}, {"v0": "permission:table-management", "v1": "api/v1/table/update/{table}", "v2": "PUT"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/roles", "v2": "POST"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/roles/:id", "v2": "PUT"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/roles/:id/status", "v2": "PUT"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/roles/:id", "v2": "DELETE"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/roles/:id", "v2": "GET"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/roles", "v2": "GET"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/employees", "v2": "GET"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/employees", "v2": "POST"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/employees/:id", "v2": "PUT"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/employees/:id/status", "v2": "PUT"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/employees/:id", "v2": "DELETE"}, {"v0": "permission:user-management", "v1": "/api/v2/merchant/employees/:id", "v2": "GET"}, {"v0": "permission:vip-management", "v1": "api/v1/vip/add", "v2": "POST"}, {"v0": "permission:vip-management", "v1": "api/v1/vip/delete/{customer}", "v2": "DELETE"}, {"v0": "permission:vip-management", "v1": "api/v1/vip/recharge-log", "v2": "GET"}, {"v0": "permission:vip-management", "v1": "api/v1/vip/update/{customer}", "v2": "PUT"}, {"v0": "permission:bill-management", "v1": "api/v1/bill/list", "v2": "GET"}, {"v0": "permission:bill-management", "v1": "api/v1/bill/detail/{id}", "v2": "GET"}, {"v0": "permission:bill-management", "v1": "api/v1/order/refund/{order}", "v2": "POST"}, {"v0": "permission:bill-management", "v1": "api/v2/bill/list", "v2": "GET"}, {"v0": "permission:bill-management", "v1": "api/v2/bill/detail/{id}", "v2": "GET"}, {"v0": "permission:bill-management", "v1": "api/v2/order/refund/{order}", "v2": "POST"}, {"v0": "permission:sms-management", "v1": "api/v1/sms/list", "v2": "GET"}, {"v0": "permission:sms-management", "v1": "api/v1/sms/pay", "v2": "GET"}, {"v0": "permission:sms-management", "v1": "api/v1/sms/qrcode", "v2": "GET"}, {"v0": "permission:sms-management", "v1": "api/v1/sms/query", "v2": "GET"}, {"v0": "permission:vip-management", "v1": "api/v1/customer/completeRecharge", "v2": "POST"}, {"v0": "permission:vip-management", "v1": "api/v1/customer/details/{customer}", "v2": "GET"}, {"v0": "permission:vip-management", "v1": "api/v1/customer/recharge", "v2": "POST"}, {"v0": "permission:handover-management", "v1": "api/v1/shift/index", "v2": "GET"}, {"v0": "permission:handover-management", "v1": "api/v2/handover/info", "v2": "GET"}, {"v0": "permission:handover-management", "v1": "api/v2/handover/handover", "v2": "POST"}, {"v0": "permission:handover-management", "v1": "api/v2/handover/handover/web", "v2": "POST"}, {"v0": "permission:handover-management", "v1": "api/v2/handover/open", "v2": "POST"}, {"v0": "permission:vip-management", "v1": "api/v1/levels", "v2": "GET"}, {"v0": "permission:vip-management", "v1": "api/v1/vip/list", "v2": "GET"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/holders", "v2": "GET"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/holders", "v2": "POST"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/holders/{id}", "v2": "GET"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/holders/{id}", "v2": "PUT"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/holders/{id}/status", "v2": "PUT"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/transactions", "v2": "GET"}, {"v0": "permission:debt-management", "v1": "api/v2/debt/transactions/{id}", "v2": "GET"}, {"v0": "permission:stock-management", "v1": "local/api/v2/foods/sell-clear/save", "v2": "POST"}, {"v0": "permission:stock-management", "v1": "local/api/v2/foods/sell-clear/del/{food_id}", "v2": "POST"}, {"v0": "permission:stock-management", "v1": "api/v1/sell-clear/all", "v2": "GET"}, {"v0": "permission:stock-management", "v1": "api/v1/sell-clear/foods", "v2": "GET"}, {"v0": "permission:stock-management", "v1": "api/v1/sell-clear/clear", "v2": "POST"}, {"v0": "permission:stock-management", "v1": "api/v1/sell-clear/delete", "v2": "POST"}, {"v0": "permission:stock-management", "v1": "api/v1/sell-clear/set-count", "v2": "POST"}, {"v0": "permission:food-management", "v1": "api/v1/food/change-support-scan-order/{food}", "v2": "GET"}, {"v0": "permission:manage-table-qrcode", "v1": "api/v1/table/qrcode/add", "v2": "POST"}, {"v0": "permission:manage-table-qrcode", "v1": "api/v1/table/qrcode/details", "v2": "GET"}, {"v0": "permission:manage-table-qrcode", "v1": "api/v1/table/qrcode/delete", "v2": "DELETE"}, {"v0": "permission:order-open", "v1": "api/v1/orders", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "api/v1/orders/{order}/finish", "v2": "PATCH"}, {"v0": "permission:order-settle-online", "v1": "api/v1/vip/phone-list", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "api/v1/customer/verify", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "api/v2/debt/holders/available", "v2": "GET"}, {"v0": "permission:order-settle-online", "v1": "api/v1/micro-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "api/v1/native-pay", "v2": "POST"}, {"v0": "permission:order-cancel", "v1": "api/v1/orders/{order}/cancel", "v2": "PATCH"}, {"v0": "permission:order-detail", "v1": "api/v1/order_details/{order}", "v2": "GET"}, {"v0": "permission:order-cancel-food", "v1": "api/v1/orders/{order}/details/{orderDetail}/cancel", "v2": "POST"}, {"v0": "permission:order-cancel-food", "v1": "api/v1/orders/{order}/cancel-all-foods", "v2": "POST"}, {"v0": "permission:order-change-table", "v1": "api/v1/orders/{order}/changeTable", "v2": "POST"}, {"v0": "permission:order-collage", "v1": "api/v1/orders/{order}/collage", "v2": "POST"}, {"v0": "permission:order-collage", "v1": "api/v1/collageorder/tables", "v2": "GET"}, {"v0": "permission:order-split", "v1": "api/v1/orders/{order}/split", "v2": "POST"}, {"v0": "permission:order-urge", "v1": "api/v1/orders/{order}/urge", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "local/api/v2/printer/add", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "local/api/v2/printer/update", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "local/api/v2/printer/del/{printer}", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "local/api/v2/printer/foods", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers", "v2": "POST"}, {"v0": "permission:printer-management", "v1": "api/v2/printers", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}", "v2": "PUT"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}", "v2": "DELETE"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/{id}/status", "v2": "PUT"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/foods", "v2": "GET"}, {"v0": "permission:printer-management", "v1": "api/v2/printers/foods", "v2": "POST"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders", "v2": "POST"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders/fast-food", "v2": "POST"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders/fast-food/delete", "v2": "POST"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders/fast-food/list", "v2": "GET"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders/fast-food/update", "v2": "POST"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/orders", "v2": "POST"}, {"v0": "permission:order-open", "v1": "local/api/v2/order/{order_id}", "v2": "GET"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/order/{order_id}", "v2": "GET"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders/add-foods/{order}", "v2": "POST"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/orders/add-foods/{order}", "v2": "POST"}, {"v0": "permission:order-open", "v1": "local/api/v2/orders/payment-list/{order}", "v2": "GET"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/orders/payment-list/{order}", "v2": "GET"}, {"v0": "permission:order-open", "v1": "local/api/v2/foodCategories", "v2": "GET"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/foodCategories", "v2": "GET"}, {"v0": "permission:order-open", "v1": "local/api/v2/tables", "v2": "GET"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/tables", "v2": "GET"}, {"v0": "permission:order-open", "v1": "local/api/v2/table-details/{order}", "v2": "GET"}, {"v0": "permission:order-open", "v1": "cloud/api/v2/table-details/{order}", "v2": "GET"}, {"v0": "permission:order-cancel-food", "v1": "local/api/v2/orders/cancel-food/{order}/{detail}", "v2": "POST"}, {"v0": "permission:order-cancel-food", "v1": "cloud/api/v2/orders/cancel-food/{order}/{detail}", "v2": "POST"}, {"v0": "permission:order-cancel-food", "v1": "local/api/v2/orders/undo-cancel-food/{order}/{detail}", "v2": "POST"}, {"v0": "permission:order-cancel-food", "v1": "cloud/api/v2/orders/undo-cancel-food/{order}/{detail}", "v2": "POST"}, {"v0": "permission:order-cancel-food", "v1": "local/api/v2/orders/cancel-all-foods/{order}", "v2": "POST"}, {"v0": "permission:order-cancel-food", "v1": "cloud/api/v2/orders/cancel-all-foods/{order}", "v2": "POST"}, {"v0": "permission:order-change-table", "v1": "local/api/v2/orders/changeTable/{order}", "v2": "POST"}, {"v0": "permission:order-change-table", "v1": "cloud/api/v2/orders/changeTable/{order}", "v2": "POST"}, {"v0": "permission:order-collage", "v1": "local/api/v2/orders/collage/{order}", "v2": "POST"}, {"v0": "permission:order-collage", "v1": "cloud/api/v2/orders/collage/{order}", "v2": "POST"}, {"v0": "permission:order-split", "v1": "local/api/v2/orders/split/{order}", "v2": "POST"}, {"v0": "permission:order-split", "v1": "cloud/api/v2/orders/split/{order}", "v2": "POST"}, {"v0": "permission:order-urge", "v1": "local/api/v2/orders/urge/{table_id}/{order_id}/{order_detail_id}", "v2": "GET"}, {"v0": "permission:order-urge", "v1": "cloud/api/v2/orders/urge/{table_id}/{order_id}/{order_detail_id}", "v2": "GET"}, {"v0": "permission:order-urge", "v1": "local/api/v2/orders/urge/all/{table_id}/{order_id}", "v2": "GET"}, {"v0": "permission:order-urge", "v1": "cloud/api/v2/orders/urge/all/{table_id}/{order_id}", "v2": "GET"}, {"v0": "permission:order-settle-offline", "v1": "local/api/v2/payment/offline", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "cloud/api/v2/payment/offline", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "local/api/v2/orders/ignore-price", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "cloud/api/v2/orders/ignore-price", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "local/api/v2/payment/reverse", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "cloud/api/v2/payment/reverse", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "local/api/v2/orders/checkout/{order}", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "local/api/v2/orders/fast-food/checkout", "v2": "POST"}, {"v0": "permission:order-settle-offline", "v1": "cloud/api/v2/orders/checkout/{order}", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/payment/micro-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/payment/micro-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/payment/qrcode", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/payment/qrcode", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/payment/customer-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/payment/customer-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/payment/debt-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/payment/debt-pay", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/orders/ignore-price", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/orders/ignore-price", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/payment/reverse", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/payment/reverse", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/payment/query/{paymentNo}", "v2": "GET"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/payment/query/{paymentNo}", "v2": "GET"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/orders/checkout/{order}", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "local/api/v2/orders/fast-food/checkout", "v2": "POST"}, {"v0": "permission:order-settle-online", "v1": "cloud/api/v2/orders/checkout/{order}", "v2": "POST"}]