# V3版本退菜接口文档

## 概述

v3版本的退菜接口基于Pid关联关系实现了智能退菜功能，支持单独退菜和连带退菜两种模式。

## 接口地址

**POST** `/api/v3/orders/cancel-food-v3/:order_id/:od_id`

## 主要特性

### 1. 基于Pid关联关系的退菜
- 支持查找所有子级记录（基于Pid字段）
- 递归处理多层级关联关系
- 确保退菜操作的完整性和一致性

### 2. 智能退菜模式
- **自动判断退菜模式**：根据被退记录的类型自动选择退菜策略
- **连带退菜**：退主菜品时自动连带退掉所有子级记录
- **单独退菜**：退子级记录时只退指定记录，不影响其他记录

### 3. 智能退菜场景支持
- **场景1**：单独退加料/餐盒 - 只退指定记录，不影响主菜品
- **场景2**：退主菜品 - 自动连带退掉所有子级记录（加料、做法、餐盒）
- **场景3**：退套餐 - 自动连带退掉所有套餐子项和子项的加料/做法/餐盒
- **场景4**：套餐子项保护 - 不允许单独退套餐子项

## 请求参数

### URL参数
- `order_id`: 订单ID
- `od_id`: 订单详情ID

### 请求体
```json
{
  "foods_count": 1.0,        // 退菜数量
  "remarks": "客户要求退菜"   // 退菜备注（可选）
}
```

## 响应结果

### 成功响应
```json
{
  "message": "操作成功",
  "data": {
    "cancelled_details": [
      {
        "id": 123,
        "food_id": 456,
        "spec_food_id": 789,
        "method_id": 0,
        "type": 1,
        "foods_count": 1.0,
        "remarks": "客户要求退菜"
      }
    ]
  }
}
```

## 使用示例

### 1. 退加料
```json
{
  "foods_count": 1.0,
  "remarks": "不要这个加料"
}
```

**结果**：只退掉指定的加料记录，主菜品不受影响。

### 2. 退主菜品（连带退菜）
```json
{
  "foods_count": 1.0,
  "remarks": "不要这个菜"
}
```

**结果**：退掉主菜品记录及其所有子级记录（加料、做法、餐盒）。

### 3. 退套餐（连带退菜）
```json
{
  "foods_count": 1.0,
  "remarks": "套餐退掉"
}
```

**结果**：退掉套餐主记录及其所有套餐子项和子项的加料/做法/餐盒。

### 4. 部分退菜
```json
{
  "foods_count": 1.0,
  "remarks": "只退一份"
}
```

**结果**：如果原来有2份，退掉1份后还剩1份。

## 退菜逻辑说明

### 智能退菜判断
系统会根据被退记录的Pid字段自动判断退菜模式：

#### 连带退菜模式（当Pid=0时）
1. 退掉主菜品记录
2. 递归查找所有子级记录（Pid=主菜品ID）
3. 退掉所有找到的子级记录
4. 继续递归处理子级的子级
5. 处理所有相关记录的库存回退

#### 单独退菜模式（当Pid>0时）
1. 只处理指定的OrderDetail记录
2. 如果是部分退菜，更新原记录数量并创建新的退菜记录
3. 如果是全部退菜，直接更新记录状态为退菜状态
4. 处理库存回退（如果启用了库存管理）
5. 不影响其他关联记录

### 数据库存储结构示例

退菜前：
```
ID  | Pid | FoodID | Type | State | 说明
----|-----|--------|------|-------|--------
100 | 0   | 123    | 1    | 2     | 主菜品
101 | 100 | 789    | 2    | 2     | 加料
102 | 100 | 456    | 4    | 2     | 做法
```

连带退主菜品后：
```
ID  | Pid | FoodID | Type | State | 说明
----|-----|--------|------|-------|--------
100 | 0   | 123    | 1    | 4     | 主菜品（已退）
101 | 100 | 789    | 2    | 4     | 加料（已退）
102 | 100 | 456    | 4    | 4     | 做法（已退）
```

单独退加料后：
```
ID  | Pid | FoodID | Type | State | 说明
----|-----|--------|------|-------|--------
100 | 0   | 123    | 1    | 2     | 主菜品（未退）
101 | 100 | 789    | 2    | 4     | 加料（已退）
102 | 100 | 456    | 4    | 2     | 做法（未退）
```

## 业务规则

### 1. 退菜权限检查
- 订单状态必须为1（新订单）或2（未支付订单）
- 菜品状态不能为3（已完成）或4（已退菜）

### 2. 套餐子项保护
- 不允许单独退套餐子项
- 如果要退套餐子项，必须退整个套餐

### 3. 数量验证
- 退菜数量不能超过原有数量
- 支持部分退菜和全部退菜

### 4. 库存处理
- 自动回退库存（如果启用了库存管理）
- 确保库存数据的准确性

### 5. 事务保证
- 所有退菜操作在数据库事务中执行
- 确保数据一致性，失败时自动回滚

## 错误处理

### 常见错误码
- `OrderNotExist`: 订单不存在
- `OrderStateNotAllowCancel`: 订单状态不允许退菜
- `OrderDetailNotFound`: 订单详情不存在
- `FoodStateNotAllowCancel`: 菜品状态不允许退菜
- `CancelQuantityExceed`: 退菜数量超过可退数量
- `CannotCancelComboSubItem`: 不能单独退套餐子项

## 与v2版本的区别

### v2版本
- 只支持单个菜品的退菜
- 不考虑关联关系
- 需要手动处理相关的加料、做法等

### v3版本
- 支持基于Pid关联关系的智能退菜
- 自动判断退菜模式（连带退菜 vs 单独退菜）
- 自动处理所有相关的子级记录
- 更完善的业务规则和错误处理
- 套餐子项保护机制

## 注意事项

1. **智能退菜判断**：系统会根据Pid字段自动判断是连带退菜还是单独退菜
2. **连带退菜影响**：退主菜品（Pid=0）会自动退掉所有相关子级记录
3. **套餐子项保护**：不允许单独退套餐子项，需要退整个套餐
4. **库存影响**：退菜会影响库存数量，需要注意库存管理
5. **打印和广播**：退菜操作会触发打印任务和广播通知
6. **不可逆操作**：退菜操作不可逆，请确认后再执行
