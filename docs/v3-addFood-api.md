# V3版本加菜接口文档

## 概述

v3版本的加菜接口在v2基础上扩展了对美食规格、做法、加料和餐盒的完整支持。

## 接口地址

- **v2兼容接口**: `POST /api/v3/orders/add-foods/:id`
- **v3扩展接口**: `POST /api/v3/orders/add-foods-v3/:id`

## 主要变化

### 1. 数据结构变化

#### v2版本 (OrderDetail)
```json
{
  "food_id": 123,
  "foods_count": 2.0,
  "remarks": "备注",
  "combo_info": null
}
```

#### v3版本 (OrderDetailV3)
```json
{
  "food_id": 123,        // 美食ID（父级菜品ID）
  "spec_food_id": 456,   // 规格ID（如果没有规格，则与food_id相同）
  "foods_count": 2.0,
  "remarks": "备注",
  "combo_info": null,
  "additions": [],       // 加料（作为子级）
  "methods": [],         // 做法（作为子级）
  "lunch_boxes": []      // 餐盒（作为子级）
}
```

### 2. 订单详情存储结构

v3版本采用父子级关系存储，使用Pid字段建立关联：

1. **规格信息作为父级**：存储在order_details表中，type=1，pid=0（或父级ID）
2. **加料作为子级**：存储在order_details表中，type=2，pid=父级订单详情ID
3. **餐盒作为子级**：存储在order_details表中，type=3，pid=父级订单详情ID
4. **做法作为子级**：存储在order_details表中，type=4，pid=父级订单详情ID

#### Pid字段说明
- **Pid=0**：表示顶级订单详情（主菜品/规格）
- **Pid>0**：表示子级订单详情，Pid值为其父级订单详情的ID
- 支持多层级关系：子级的子级也可以有自己的子级

### 3. 规格处理逻辑

- 菜品和规格共用foods表，使用pid字段进行绑定
- 如果没有规格，将菜品自己看成默认规格（spec_food_id = food_id）
- 如果有规格，spec_food_id为具体的规格ID

## 请求示例

### 基础菜品（无规格）
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 123,  // 与food_id相同，表示无规格
      "foods_count": 1.0,
      "remarks": "不要辣"
    }
  ],
  "order_remark": "订单备注"
}
```

### 带规格的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,      // 父级菜品ID
      "spec_food_id": 456, // 规格ID
      "foods_count": 1.0,
      "remarks": "中辣"
    }
  ],
  "order_remark": "订单备注"
}
```

### 带加料的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "remarks": "主菜",
      "additions": [
        {
          "food_id": 789,    // 加料ID
          "foods_count": 1.0,
          "remarks": "加料备注"
        }
      ]
    }
  ],
  "order_remark": "订单备注"
}
```

### 带做法的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "remarks": "主菜",
      "methods": [
        {
          "method_id": 101,
          "group_id": 201,
          "count": 1
        }
      ]
    }
  ],
  "order_remark": "订单备注"
}
```

### 带餐盒的菜品
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "remarks": "主菜",
      "lunch_boxes": [
        {
          "food_id": 301,    // 餐盒ID
          "spec_food_id": 301,
          "foods_count": 1.0,
          "remarks": "餐盒备注"
        }
      ]
    }
  ],
  "order_remark": "订单备注"
}
```

### 复合示例（包含所有类型）
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 2.0,
      "remarks": "主菜，中辣",
      "additions": [
        {
          "food_id": 789,
          "foods_count": 1.0,
          "remarks": "加鸡蛋"
        }
      ],
      "methods": [
        {
          "method_id": 101,
          "group_id": 201,
          "count": 1
        }
      ],
      "lunch_boxes": [
        {
          "food_id": 301,
          "spec_food_id": 301,
          "foods_count": 2.0,
          "remarks": "大号餐盒"
        }
      ]
    }
  ],
  "order_remark": "整个订单的备注"
}
```

## 套餐处理

v3版本对套餐进行了完整的处理：

### 1. 套餐主记录
- 套餐作为父级（is_combo=true），Pid=0
- 保持ComboInfo字段不变（用于兼容性）

### 2. 套餐子项处理
- 遍历ComboInfo中的每个套餐子项
- 为每个子项创建独立的OrderDetail记录，Pid=套餐主记录ID
- 支持套餐子项的规格选择（SpecFoodID）

### 3. 套餐子项的子级处理
如果套餐子项包含加料、做法或餐盒：
- **加料**：创建子级OrderDetail记录，Type=2，Pid=套餐子项ID
- **做法**：创建子级OrderDetail记录，Type=4，Pid=套餐子项ID
- **餐盒**：创建子级OrderDetail记录，Type=3，Pid=套餐子项ID

### 4. 扩展的ComboInfo结构
```json
{
  "id": 1,
  "food_id": 123,
  "spec_food_id": 456,
  "count": 1.0,
  "remarks": "套餐子项备注",
  "additions": [
    {
      "food_id": 789,
      "count": 1.0,
      "remarks": "加料备注"
    }
  ],
  "methods": [
    {
      "method_id": 101,
      "group_id": 201,
      "count": 1
    }
  ],
  "lunch_boxes": [
    {
      "food_id": 301,
      "spec_food_id": 301,
      "count": 1.0,
      "remarks": "餐盒备注"
    }
  ]
}
```

### 5. 套餐完整示例
```json
{
  "order_details": [
    {
      "food_id": 100,      // 套餐ID
      "spec_food_id": 100,
      "foods_count": 1.0,
      "remarks": "套餐主菜",
      "combo_info": [
        {
          "id": 1,
          "food_id": 123,    // 套餐子项1：主菜
          "spec_food_id": 456,
          "count": 1.0,
          "remarks": "主菜中辣",
          "additions": [
            {
              "food_id": 789,  // 主菜的加料
              "count": 1.0,
              "remarks": "加鸡蛋"
            }
          ],
          "methods": [
            {
              "method_id": 101,
              "group_id": 201,
              "count": 1
            }
          ]
        },
        {
          "id": 2,
          "food_id": 124,    // 套餐子项2：配菜
          "spec_food_id": 124,
          "count": 1.0,
          "remarks": "配菜",
          "lunch_boxes": [
            {
              "food_id": 301,  // 配菜的餐盒
              "spec_food_id": 301,
              "count": 1.0,
              "remarks": "小号餐盒"
            }
          ]
        }
      ]
    }
  ],
  "order_remark": "套餐订单"
}
```
套餐完整的存储结构层次:
```js
套餐主记录 (Pid=0, Type=1, IsCombo=true)
├── 套餐子项1 (Pid=套餐主记录ID, Type=1)
│   ├── 子项1的加料 (Pid=套餐子项1ID, Type=2)
│   ├── 子项1的做法 (Pid=套餐子项1ID, Type=4)
│   └── 子项1的餐盒 (Pid=套餐子项1ID, Type=3)
├── 套餐子项2 (Pid=套餐主记录ID, Type=1)
│   └── 子项2的加料 (Pid=套餐子项2ID, Type=2)
└── 普通加料/做法/餐盒 (Pid=套餐主记录ID, Type=2/3/4)
```

对应的数据库存储结构：
```
ID  | Pid | FoodID | SpecFoodID | Type | 说明
----|-----|--------|------------|------|--------
200 | 0   | 100    | 100        | 1    | 套餐主记录
201 | 200 | 123    | 456        | 1    | 套餐子项1（主菜）
202 | 201 | 789    | 789        | 2    | 主菜的加料
203 | 201 | NULL   | NULL       | 4    | 主菜的做法（MethodID=101）
204 | 200 | 124    | 124        | 1    | 套餐子项2（配菜）
205 | 204 | 301    | 301        | 3    | 配菜的餐盒
```

## 向后兼容性

- v2接口（`/api/v3/orders/add-foods/:id`）继续支持原有的OrderAddFoodRequest结构
- v3接口（`/api/v3/orders/add-foods-v3/:id`）使用新的OrderAddFoodRequestV3结构
- 现有v2客户端无需修改即可继续使用

## 核心特性

### 1. 父子级关系处理
- **规格信息作为父级**：存储主要的菜品信息，Pid=0
- **加料/做法/餐盒作为子级**：分别存储到order_details表中，Pid=父级ID
- 支持递归处理（子级的子级）

### 2. 简化的处理流程
- **NewFoodV3**：只创建主订单详情（规格级别），返回单个OrderDetailModel
- **createAllSubOrderDetails**：处理所有子级详情的创建和保存
- **分步骤处理**：先保存主级获取ID，再创建子级设置Pid关联

### 3. Pid字段关联
- 使用Pid字段建立美食与其做法、加料、餐盒的关联关系
- 支持多层级嵌套结构
- 确保数据一致性和完整性

### 4. 完整的套餐支持
- **扩展ComboInfo结构**：支持套餐子项的加料、做法、餐盒
- **独立存储套餐子项**：每个套餐子项作为独立的OrderDetail记录
- **正确的关联关系**：套餐子项Pid=套餐主记录ID，子项的加料/做法/餐盒Pid=套餐子项ID
- **保持兼容性**：ComboInfo字段保持不变，确保向后兼容

### 5. 事务一致性
- 所有记录在同一个数据库事务中创建
- 分步骤处理：主记录 -> 套餐子项 -> 普通子级
- 确保数据完整性和一致性

## 数据库存储示例

假设有以下请求：
```json
{
  "order_details": [
    {
      "food_id": 123,
      "spec_food_id": 456,
      "foods_count": 1.0,
      "additions": [
        {
          "food_id": 789,
          "foods_count": 1.0
        }
      ],
      "methods": [
        {
          "method_id": 101,
          "group_id": 201,
          "count": 1
        }
      ]
    }
  ]
}
```

在order_details表中的存储结构：
```
ID  | Pid | FoodID | SpecFoodID | MethodID | Type | 说明
----|-----|--------|------------|----------|------|--------
100 | 0   | 123    | 456        | NULL     | 1    | 主菜品（规格）
101 | 100 | 789    | 789        | NULL     | 2    | 加料（父级ID=100）
102 | 100 | NULL   | NULL       | 101      | 4    | 做法（父级ID=100）
```

## 错误处理

新增的错误码：
- `FoodSpecNotFound`: 美食规格不存在
- `FoodSpecFoodNotFound`: 规格对应的美食不存在
- `MethodNotFound`: 做法不存在
- `MethodGroupMismatch`: 做法分组不匹配
- `FoodAdditionNotFound`: 加料不存在
- `LunchBoxNotFound`: 餐盒不存在
