package route

import (
	"context"
	"ros-api-go/internal/http/controller/v2/foods"
	"ros-api-go/internal/http/middleware"

	"github.com/gin-gonic/gin"
)

type FoodRouterGroup struct {
	FoodController              *foods.FoodController
	FoodComboController         *foods.FoodComboController
	FoodCategoryController      *foods.FoodCategoryController
	FoodSpecController          *foods.FoodSpecController
	MethodGroupController       *foods.MethodGroupController
	MethodController            *foods.MethodController
	FoodAdditionGroupController *foods.FoodAdditionGroupController
	FoodAdditionController      *foods.FoodAdditionController
	LunchBoxController          *foods.LunchBoxController
}

func (r *FoodRouterGroup) Register(ctx context.Context, router *gin.RouterGroup) {
	foodsApi := router.Group("foods").Use(middleware.AuthMiddleware())
	{
		// 获取美食列表（分页）
		foodsApi.GET("", r.FoodController.GetFoodsList)
		// 获取单个美食
		foodsApi.GET("/:id", r.FoodController.GetFoodDetail)
		// 获取父级美食列表（用于规格/做法/加料关联）
		foodsApi.GET("parents", r.FoodController.GetParentFoodsForSpecAssociation)
		// 获取规格级别美食列表（用于餐盒关联）
		foodsApi.GET("specs", r.FoodController.GetSpecFoodsForAssociation)
		// 创建美食
		foodsApi.POST("", middleware.CasbinMiddleware(), r.FoodController.CreateFood)
		// 更新美食
		foodsApi.PUT("/:id", middleware.CasbinMiddleware(), r.FoodController.UpdateFood)
		// 创建套餐
		foodsApi.POST("combo", middleware.CasbinMiddleware(), r.FoodComboController.CreateComboFood)
		// 更新套餐
		foodsApi.PUT("combo/:id", middleware.CasbinMiddleware(), r.FoodComboController.UpdateComboFood)
		// 获取双屏显示食品列表
		foodsApi.GET("/dual", r.FoodController.FoodListForDualScreen)
		// 获取默认的美食图片
		foodsApi.GET("/default-images", r.FoodController.GetDefaultFoodImages)
		// 保存美食排序
		foodsApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodController.SaveFoodSort)
		// 修改美食状态
		foodsApi.PUT("/:id/state", r.FoodController.ChangeState)
		// 修改美食是否支持扫码点单
		foodsApi.PUT("/:id/support-scan-order", r.FoodController.ChangeSupportScanOrder)
		// 删除美食
		foodsApi.DELETE("/:id", r.FoodController.Destroy)
	}

	foodCategoryApi := router.Group("foodCategories").Use(middleware.AuthMiddleware())
	{
		// 获取菜品分类列表（分页）
		foodCategoryApi.GET("", r.FoodCategoryController.GetFoodCategoryList)
		// 获取单个菜品分类
		foodCategoryApi.GET("/:id", r.FoodCategoryController.GetFoodCategory)
		// 创建菜品分类
		foodCategoryApi.POST("", r.FoodCategoryController.CreateFoodCategory)
		// 更新菜品分类
		foodCategoryApi.PUT("/:id", r.FoodCategoryController.UpdateFoodCategory)
		// 删除菜品分类
		foodCategoryApi.DELETE("/:id", r.FoodCategoryController.DeleteFoodCategory)
		// 保存美食分类排序
		foodCategoryApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodCategoryController.SaveFoodCategorySort)
		// 更新菜品分类状态
		foodCategoryApi.PUT("/:id/state", r.FoodCategoryController.UpdateFoodCategoryState)
	}

	// 规格管理路由
	foodSpecApi := router.Group("food-specs").Use(middleware.AuthMiddleware())
	{
		// 获取规格列表
		foodSpecApi.GET("", r.FoodSpecController.GetFoodSpecList)
		// 获取单个规格
		foodSpecApi.GET("/:id", r.FoodSpecController.GetFoodSpec)
		// 获取规格关联的美食列表
		foodSpecApi.GET("/:id/foods", r.FoodSpecController.GetSpecFoods)
		// 保存规格排序
		foodSpecApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodSpecController.SaveFoodSpecSort)
		// 保存规格美食关联关系
		foodSpecApi.POST("/foods", middleware.CasbinMiddleware(), r.FoodSpecController.SaveFoodSpecFoods)
		// 解除规格美食关联关系
		foodSpecApi.DELETE("/foods", middleware.CasbinMiddleware(), r.FoodSpecController.RemoveFoodSpecFoods)
		// 创建规格
		foodSpecApi.POST("", middleware.CasbinMiddleware(), r.FoodSpecController.CreateFoodSpec)
		// 更新规格
		foodSpecApi.PUT("/:id", middleware.CasbinMiddleware(), r.FoodSpecController.UpdateFoodSpec)
		// 删除规格
		foodSpecApi.DELETE("/:id", middleware.CasbinMiddleware(), r.FoodSpecController.DeleteFoodSpec)
	}

	methodGroupsApi := router.Group("method-groups").Use(middleware.AuthMiddleware())
	{
		// 获取做法分组列表
		methodGroupsApi.GET("", r.MethodGroupController.GetMethodGroupList)
		// 创建做法分组
		methodGroupsApi.POST("", middleware.CasbinMiddleware(), r.MethodGroupController.CreateMethodGroup)
		// 更新做法分组
		methodGroupsApi.PUT("/:id", middleware.CasbinMiddleware(), r.MethodGroupController.UpdateMethodGroup)
		// 删除做法分组
		methodGroupsApi.DELETE("/:id", middleware.CasbinMiddleware(), r.MethodGroupController.DeleteMethodGroup)
		// 保存做法分组排序
		methodGroupsApi.POST("/sort", middleware.CasbinMiddleware(), r.MethodGroupController.SaveMethodGroupSort)
	}

	methodsApi := router.Group("methods").Use(middleware.AuthMiddleware())
	{
		// 获取做法列表
		methodsApi.GET("", r.MethodController.GetMethodList)
		// 获取单个做法
		methodsApi.GET("/:id", r.MethodController.GetMethodByID)
		// 创建做法
		methodsApi.POST("", middleware.CasbinMiddleware(), r.MethodController.CreateMethod)
		// 更新做法
		methodsApi.PUT("/:id", middleware.CasbinMiddleware(), r.MethodController.UpdateMethod)
		// 删除做法
		methodsApi.DELETE("/:id", middleware.CasbinMiddleware(), r.MethodController.DeleteMethod)
		// 保存做法排序
		methodsApi.POST("/sort", middleware.CasbinMiddleware(), r.MethodController.SaveMethodSort)
		// 获取做法关联的美食列表
		methodsApi.GET("/:id/foods", r.MethodController.GetMethodFoods)
		// 保存美食做法关联
		methodsApi.POST("/foods", middleware.CasbinMiddleware(), r.MethodController.SaveFoodMethod)
	}

	// 加料分组管理路由
	foodAdditionGroupApi := router.Group("food-addition-groups").Use(middleware.AuthMiddleware())
	{
		// 获取菜品分类列表（分页）
		foodAdditionGroupApi.GET("", r.FoodAdditionGroupController.GetFoodAdditionGroupList)
		// 创建菜品分类
		foodAdditionGroupApi.POST("", r.FoodAdditionGroupController.CreateFoodAdditionGroup)
		// 更新菜品分类
		foodAdditionGroupApi.PUT("/:id", r.FoodAdditionGroupController.UpdateFoodAdditionGroup)
		// 删除菜品分类
		foodAdditionGroupApi.DELETE("/:id", r.FoodAdditionGroupController.DeleteFoodAdditionGroup)
		// 保存美食分类排序
		foodAdditionGroupApi.POST("/sort", middleware.CasbinMiddleware(), r.FoodAdditionGroupController.SaveFoodAdditionGroupSort)
	}

	// 加料管理路由
	foodAdditionsApi := router.Group("food-additions").Use(middleware.AuthMiddleware())
	{
		// 获取加料列表
		foodAdditionsApi.GET("", r.FoodAdditionController.GetFoodAdditionList)
		// 创建加料
		foodAdditionsApi.POST("", middleware.CasbinMiddleware(), r.FoodAdditionController.CreateFoodAddition)
		// 更新加料
		foodAdditionsApi.PUT("/:id", middleware.CasbinMiddleware(), r.FoodAdditionController.UpdateFoodAddition)
		// 删除加料
		foodAdditionsApi.DELETE("/:id", middleware.CasbinMiddleware(), r.FoodAdditionController.DeleteFoodAddition)
		// 获取加料关联的美食列表
		foodAdditionsApi.GET("/:id/foods", r.FoodAdditionController.GetAdditionFoods)
		// 关联菜品
		foodAdditionsApi.POST("/foods", middleware.CasbinMiddleware(), r.FoodAdditionController.SaveFoodAdditionFoods)
		// 解除关联
		foodAdditionsApi.DELETE("/foods", middleware.CasbinMiddleware(), r.FoodAdditionController.RemoveFoodAdditionFoods)
	}

	// 餐盒管理路由
	lunchBoxesApi := router.Group("lunch-boxes").Use(middleware.AuthMiddleware())
	{
		// 获取餐盒列表
		lunchBoxesApi.GET("", r.LunchBoxController.GetLunchBoxList)
		// 创建餐盒
		lunchBoxesApi.POST("", middleware.CasbinMiddleware(), r.LunchBoxController.CreateLunchBox)
		// 更新餐盒
		lunchBoxesApi.PUT("/:id", middleware.CasbinMiddleware(), r.LunchBoxController.UpdateLunchBox)
		// 保存餐盒排序
		lunchBoxesApi.POST("/sort", middleware.CasbinMiddleware(), r.LunchBoxController.SaveLunchBoxSort)
		// 删除餐盒
		lunchBoxesApi.DELETE("/:id", middleware.CasbinMiddleware(), r.LunchBoxController.DeleteLunchBox)
		// 获取餐盒关联的美食列表
		lunchBoxesApi.GET("/:id/foods", r.LunchBoxController.GetLunchBoxFoods)
		// 关联菜品
		lunchBoxesApi.POST("/foods", middleware.CasbinMiddleware(), r.LunchBoxController.SaveLunchBoxFoods)
		// 解除关联
		lunchBoxesApi.DELETE("/foods", middleware.CasbinMiddleware(), r.LunchBoxController.RemoveLunchBoxFoods)
	}
}
