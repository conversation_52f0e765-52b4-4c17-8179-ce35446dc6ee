package route

import (
	"context"
	v2 "ros-api-go/internal/http/controller/v2"
	orderV2 "ros-api-go/internal/http/controller/v2/order"
	"ros-api-go/internal/http/controller/v2/statistics"
	"ros-api-go/internal/http/middleware"

	"github.com/gin-gonic/gin"
)

type ApiRouterGroup struct {
	SyncRouterGroup             *SyncRouterGroup
	MerchantRouterGroup         *MerchantRouterGroup
	PayRouterGroup              *PayRouterGroup
	LocalRouterGroup            *LocalRouterGroup
	ScanRouterGroup             *ScanRouterGroup
	FoodRouterGroup             *FoodRouterGroup
	AuthController              *v2.AuthController
	OperationPasswordController *v2.OperationPasswordController
	OrderController             *orderV2.OrderRefundController
	BillController              *v2.BillController
	RPCInvokeController         *v2.RPCInvokeController
	MqttController              *v2.MqttController
	PermissionController        *v2.PermissionController
	HandoverController          *v2.HandoverController
	UserController              *v2.UserController
	PrinterController           *v2.PrinterController
	TerminalController          *v2.TerminalController
	DebtHolderController        *v2.DebtHolderController
	DebtTransactionController   *v2.DebtTransactionController
	BusinessStatisticController *statistics.BusinessStatisticController
	FoodsStatisticController    *statistics.FoodsStatisticController
	HandoverStatisticController *statistics.HandoverStatisticController
	GraphStatisticController    *statistics.GraphStatisticController
	MasterStatisticController   *statistics.MasterStatisticController
}

func (router *ApiRouterGroup) RegisterRouters(ctx context.Context, group *gin.RouterGroup) {
	v2Group := group.Group("v2")
	router.PayRouterGroup.Register(ctx, v2Group)
	router.SyncRouterGroup.Register(ctx, v2Group)
	router.MerchantRouterGroup.Register(ctx, v2Group)
	router.LocalRouterGroup.Register(ctx, v2Group)
	router.ScanRouterGroup.Register(ctx, v2Group)
	router.FoodRouterGroup.Register(ctx, v2Group)
	router.registerV2Routes(ctx, v2Group)
}

func (router *ApiRouterGroup) registerV2Routes(ctx context.Context, group *gin.RouterGroup) {

	group.POST("auth/merchants", router.AuthController.GetUserMerchants)
	group.POST("auth/login", router.AuthController.UserLogin)
	group.POST("auth/master-login", router.AuthController.ManagerLogin)
	group.POST("auth/verify-sms", router.AuthController.ResetPassword)                 // todo: deprecated
	group.POST("auth/send-verify-sms", router.AuthController.SendResetPasswordSmsCode) // todo: deprecated
	group.POST("auth/reset-password-code", router.AuthController.SendResetPasswordSmsCode)
	group.POST("auth/reset-password", router.AuthController.ResetPassword)
	group.POST("auth/operation-password-code", middleware.AuthMiddleware(), router.OperationPasswordController.SendOperationPasswordSmsCode)
	group.POST("auth/operation-password", middleware.AuthMiddleware(), router.OperationPasswordController.SetOperationPassword)

	auth := group.Group("auth")
	{
		auth.GET("info", middleware.AuthMiddleware(), router.AuthController.Info)
		auth.POST("logout", middleware.AuthMiddleware(), router.AuthController.Logout)
	}

	group.GET("user/my-orders", middleware.AuthMiddleware(), router.UserController.MyOrders)
	group.GET("user/my-orders/:order_id", middleware.AuthMiddleware(), router.UserController.GetOrder)

	bill := group.Group("bill", middleware.AuthMiddleware(), middleware.CasbinMiddleware())
	{
		bill.GET("list", router.BillController.GetList)
		bill.GET("detail/:orderId", router.BillController.GetDetail)
	}

	order := group.Group("order", middleware.AuthMiddleware())
	{
		order.POST("refund/:orderId", middleware.CasbinMiddleware(), router.OrderController.OrderRefund) // 订单退款（退菜）
	}

	group.POST("rpc/invoke", router.RPCInvokeController.Post)
	group.GET("permissions", router.PermissionController.List)

	// 打印机管理
	printers := group.Group("/printers", middleware.AuthMiddleware(), middleware.CasbinMiddleware())
	{
		printers.GET("", router.PrinterController.GetList)
		printers.POST("", router.PrinterController.Create)
		printers.GET("/:id", router.PrinterController.Get)
		printers.PUT("/:id", router.PrinterController.Update)
		printers.DELETE("/:id", router.PrinterController.Delete)
		printers.PUT("/:id/status", router.PrinterController.UpdateStatus)
		printers.GET("/foods", router.PrinterController.GetFoodPrinters)
		printers.POST("/foods", router.PrinterController.SavePrinterFoods)
	}

	mqtt := group.Group("mqtt")
	{
		mqtt.GET("config", middleware.ServerAuthMiddleware(), router.MqttController.Config)
	}

	handover := group.Group("handover")
	{
		handover.POST("open", middleware.AuthMiddleware(), middleware.CasbinMiddleware(), router.HandoverController.Open)
		handover.GET("info", middleware.AuthMiddleware(), middleware.CasbinMiddleware(), router.HandoverController.HandoverInfo)
		handover.POST("handover", middleware.ServerAuthMiddleware(), router.HandoverController.HandoverFromCashier)
		handover.POST("handover/web", middleware.AuthMiddleware(), router.HandoverController.HandoverFromWeb)
	}

	// 统计信息
	statistic := group.Group("statistics", middleware.AuthMiddleware(), middleware.CasbinMiddleware())
	{
		statistic.GET("business", router.BusinessStatisticController.GetStatistics)
		statistic.GET("foods", router.FoodsStatisticController.GetStatistics)
		statistic.GET("handover", router.HandoverStatisticController.GetStatistics)
		statistic.GET("graph", router.GraphStatisticController.GetStatistics)
		statistic.GET("master-home", router.MasterStatisticController.GetStatistics)
	}

	// 终端管理
	group.GET("terminals", router.TerminalController.GetActiveTerminals)

	// 赊账相关接口
	debt := group.Group("debt", middleware.AuthMiddleware(), middleware.CasbinMiddleware())
	{
		// 赊账人管理
		debtHolders := debt.Group("holders")
		{
			debtHolders.GET("", router.DebtHolderController.GetList)
			debtHolders.GET("available", router.DebtHolderController.GetAvailableList)
			debtHolders.POST("", router.DebtHolderController.Create)
			debtHolders.GET(":id", router.DebtHolderController.Get)
			debtHolders.PUT(":id", router.DebtHolderController.Update)
			debtHolders.PUT(":id/status", router.DebtHolderController.UpdateStatus)
		}

		// 赊账交易记录
		debtTransactions := debt.Group("transactions")
		{
			debtTransactions.GET("", router.DebtTransactionController.GetList)
			debtTransactions.GET(":id", router.DebtTransactionController.Get)
		}
	}

}
