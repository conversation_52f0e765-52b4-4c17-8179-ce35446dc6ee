package food_service

import (
	"context"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// LunchBoxService 餐盒业务逻辑
type LunchBoxService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetLunchBoxCategory 获取餐盒分类
func (serv *LunchBoxService) GetLunchBoxCategory(ctx context.Context, merchantNo string) (*model.FoodCategoryModel, error) {
	var category model.FoodCategoryModel
	query := util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("merchant_no = ? AND type = ? AND state = ?", merchantNo, model.FoodCategoryTypeLunchBox, 1)
	one, err := util.FindOne(ctx, query, &category)
	if err != nil {
		return nil, err
	}
	// 如果不存在则创建
	if !one {
		category = model.FoodCategoryModel{
			MerchantNo: merchantNo,
			Type:       model.FoodCategoryTypeLunchBox,
			NameUg:     "تاماق قاچىسى",
			NameZh:     "餐盒分类",
			Sort:       999,
			State:      1,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}
		err = util.GetDB(ctx, serv.DB).Create(&category).Error
		if err != nil {
			return nil, err
		}
	}
	return &category, nil
}

// GetLunchBoxList 获取餐盒列表（分页）
func (serv *LunchBoxService) GetLunchBoxList(ctx context.Context, req *food_request.LunchBoxListRequest, merchantNo string) ([]*model.FoodModel, error) {
	query := util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND type = ? AND deleted_at IS NULL", merchantNo, model.FoodTypeLunchBox). // type=3 表示餐盒
		Select("foods.*, (SELECT COUNT(*) FROM food_lunch_box WHERE lunch_box_id = foods.id) as associated_foods")

	// 按状态筛选
	if req.State != nil && *req.State >= 0 {
		query = query.Where("state = ?", *req.State)
	}

	// 搜索关键词
	if req.Keyword != "" {
		query = query.Where("(name_zh LIKE ? OR name_ug LIKE ?)", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 按排序字段排序
	query = query.Order("sort ASC, id DESC")

	var list []*model.FoodModel

	return list, query.Find(&list).Error
}

// CreateLunchBox 创建餐盒
func (serv *LunchBoxService) CreateLunchBox(ctx context.Context, req *food_request.CreateLunchBoxRequest, merchantNo string) (*model.FoodModel, error) {
	// 获取餐盒分类(不存在则创建)
	category, err := serv.GetLunchBoxCategory(ctx, merchantNo)
	if err != nil {
		return nil, err
	}

	// 检查名称是否已存在
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND type = ? AND (name_ug = ? OR name_zh = ?) AND deleted_at IS NULL", merchantNo, model.FoodTypeLunchBox, req.NameUg, req.NameZh).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "LunchBoxNameExists")
	}

	// 如果没有提供排序值，使用数据库默认值
	sort := req.Sort
	if sort == 0 {
		var maxSort int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("merchant_no = ? AND type = ? AND deleted_at IS NULL", merchantNo, model.FoodTypeLunchBox).
			Select("COALESCE(MAX(sort), 0)").Scan(&maxSort).Error
		if err != nil {
			return nil, err
		}
		sort = maxSort + 1
	}

	// 创建餐盒记录
	lunchBox := &model.FoodModel{
		MerchantNo:       merchantNo,
		Type:             model.FoodTypeLunchBox, // 餐盒类型
		FoodCategoryID:   category.ID,
		Image:            req.Image,
		ShortcutCode:     req.ShortcutCode,
		NameUg:           req.NameUg,
		NameZh:           req.NameZh,
		CostPrice:        req.CostPrice,
		VipPrice:         req.VipPrice,
		Price:            req.Price,
		FormatID:         req.FormatID,
		IsSpecialFood:    req.IsSpecialFood,
		SupportScanOrder: req.SupportScanOrder,
		Sort:             sort,
		State:            req.State,
		IsSync:           false,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = util.GetDB(ctx, serv.DB).Create(lunchBox).Error
	if err != nil {
		return nil, err
	}

	return lunchBox, nil
}

// UpdateLunchBox 更新餐盒
func (serv *LunchBoxService) UpdateLunchBox(ctx context.Context, lunchBoxID int64, req *food_request.UpdateLunchBoxRequest, merchantNo string) (*model.FoodModel, error) {
	// 检查餐盒是否存在且属于该商户
	var lunchBox model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", lunchBoxID, merchantNo, model.FoodTypeLunchBox), &lunchBox)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "LunchBoxNotFound")
	}

	// 检查名称是否已存在（排除当前记录）
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND type = ? AND (name_ug = ? OR name_zh = ?) AND id != ? AND deleted_at IS NULL", merchantNo, model.FoodTypeLunchBox, req.NameUg, req.NameZh, lunchBoxID).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "LunchBoxNameExists")
	}

	// 更新餐盒信息
	lunchBox.Image = req.Image
	lunchBox.ShortcutCode = req.ShortcutCode
	lunchBox.NameUg = req.NameUg
	lunchBox.NameZh = req.NameZh
	lunchBox.CostPrice = req.CostPrice
	lunchBox.VipPrice = req.VipPrice
	lunchBox.Price = req.Price
	lunchBox.FormatID = req.FormatID
	lunchBox.IsSpecialFood = req.IsSpecialFood
	lunchBox.SupportScanOrder = req.SupportScanOrder
	lunchBox.Sort = req.Sort
	lunchBox.State = req.State
	lunchBox.UpdatedAt = time.Now()

	err = util.GetDB(ctx, serv.DB).Save(&lunchBox).Error
	if err != nil {
		return nil, err
	}

	return &lunchBox, nil
}

// SaveLunchBoxSort 保存餐盒排序
func (serv *LunchBoxService) SaveLunchBoxSort(ctx context.Context, req *food_request.SaveLunchBoxSortRequest, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		for _, item := range req.LunchBoxes {
			// 检查餐盒是否存在且属于该商户
			var lunchBox model.FoodModel
			one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
				Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", item.ID, merchantNo, model.FoodTypeLunchBox), &lunchBox)
			if err != nil {
				return err
			}
			if !one {
				return errors.NotFound("", "LunchBoxNotFound")
			}

			// 更新排序
			err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
				Where("id = ?", item.ID).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// DeleteLunchBox 删除餐盒
func (serv *LunchBoxService) DeleteLunchBox(ctx context.Context, lunchBoxID int64, merchantNo string) error {
	// 检查餐盒是否存在且属于该商户
	var lunchBox model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", lunchBoxID, merchantNo, model.FoodTypeLunchBox), &lunchBox)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "LunchBoxNotFound")
	}

	// 检查是否有关联的菜品
	var associationCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodLunchBoxModel{}).
		Where("lunch_box_id = ? AND merchant_no = ?", lunchBoxID, merchantNo).
		Count(&associationCount).Error
	if err != nil {
		return err
	}
	if associationCount > 0 {
		return errors.BadRequest("", "LunchBoxHasAssociatedFoods")
	}

	// 软删除餐盒
	now := time.Now()
	err = util.GetDB(ctx, serv.DB).Model(&lunchBox).Update("deleted_at", &now).Error
	if err != nil {
		return err
	}

	return nil
}

// GetLunchBoxFoods 获取餐盒关联的美食列表
func (serv *LunchBoxService) GetLunchBoxFoods(ctx context.Context, lunchBoxID int64, req *food_request.LunchBoxFoodsRequest, merchantNo string) (*model.FoodModel, []*model.FoodLunchBoxModel, error) {
	// 检查餐盒是否存在
	var lunchBox model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", lunchBoxID, merchantNo, model.FoodTypeLunchBox), &lunchBox)
	if err != nil {
		return nil, nil, err
	}
	if !one {
		return nil, nil, errors.NotFound("", "LunchBoxNotFound")
	}

	query := util.GetDB(ctx, serv.DB).Model(&model.FoodLunchBoxModel{}).
		Where("lunch_box_id = ? AND merchant_no = ?", lunchBoxID, merchantNo).
		Preload("Food.FoodCategory").
		Order("created_at DESC")

	var list []*model.FoodLunchBoxModel

	return &lunchBox, list, query.Find(&list).Error
}

// SaveLunchBoxFoods 关联菜品
func (serv *LunchBoxService) SaveLunchBoxFoods(ctx context.Context, req *food_request.SaveLunchBoxFoodsRequest, merchantNo string) error {
	// 检查餐盒是否存在
	var lunchBox model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", req.LunchBoxID, merchantNo, model.FoodTypeLunchBox), &lunchBox)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "LunchBoxNotFound")
	}

	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 提取所有食品ID用于批量查询
		foodIDs := make([]int64, len(req.Foods))
		for i, foodItem := range req.Foods {
			foodIDs[i] = foodItem.FoodID
		}

		// 批量检查菜品是否存在
		var existingFoods []model.FoodModel
		err := util.GetDB(ctx, serv.DB).
			Where("id IN ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", foodIDs, merchantNo, model.FoodTypeFood).
			Find(&existingFoods).Error
		if err != nil {
			return err
		}

		// 创建存在的菜品ID映射
		existingFoodMap := make(map[int64]bool)
		for _, food := range existingFoods {
			existingFoodMap[food.ID] = true
		}

		// 验证所有菜品都存在
		for _, foodItem := range req.Foods {
			if !existingFoodMap[foodItem.FoodID] {
				return errors.BadRequest("", "FoodNotFound")
			}
		}

		// 批量查询现有关联关系
		var existingAssociations []model.FoodLunchBoxModel
		err = util.GetDB(ctx, serv.DB).
			Where("food_id IN ? AND lunch_box_id = ? AND merchant_no = ?",
				foodIDs, req.LunchBoxID, merchantNo).
			Find(&existingAssociations).Error
		if err != nil {
			return err
		}

		// 创建现有关联关系映射
		existingAssociationMap := make(map[int64]*model.FoodLunchBoxModel)
		for i := range existingAssociations {
			existingAssociationMap[existingAssociations[i].FoodID] = &existingAssociations[i]
		}

		// 准备批量操作的数据
		var toCreate []*model.FoodLunchBoxModel
		var toUpdateRecords []*model.FoodLunchBoxModel
		now := time.Now()

		for _, foodItem := range req.Foods {
			if existing, exists := existingAssociationMap[foodItem.FoodID]; exists {
				// 需要更新的记录
				existing.Count = foodItem.Count
				existing.UpdatedAt = now
				toUpdateRecords = append(toUpdateRecords, existing)
			} else {
				// 需要创建的记录
				toCreate = append(toCreate, &model.FoodLunchBoxModel{
					MerchantNo: merchantNo,
					FoodID:     foodItem.FoodID,
					LunchBoxID: req.LunchBoxID,
					Count:      foodItem.Count,
					CreatedAt:  now,
					UpdatedAt:  now,
				})
			}
		}

		// 批量创建新关联关系
		if len(toCreate) > 0 {
			err = util.GetDB(ctx, serv.DB).CreateInBatches(toCreate, 100).Error
			if err != nil {
				return err
			}
		}

		// 批量更新现有关联关系
		if len(toUpdateRecords) > 0 {
			err = util.GetDB(ctx, serv.DB).Save(toUpdateRecords).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// RemoveLunchBoxFoods 解除关联
func (serv *LunchBoxService) RemoveLunchBoxFoods(ctx context.Context, req *food_request.RemoveLunchBoxFoodsRequest, merchantNo string) error {
	// 检查餐盒是否存在
	var lunchBox model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", req.LunchBoxID, merchantNo, model.FoodTypeLunchBox), &lunchBox)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "LunchBoxNotFound")
	}

	// 删除关联关系
	err = util.GetDB(ctx, serv.DB).
		Where("lunch_box_id = ? AND food_id IN ? AND merchant_no = ?", req.LunchBoxID, req.FoodIDs, merchantNo).
		Delete(&model.FoodLunchBoxModel{}).Error
	if err != nil {
		return err
	}

	return nil
}
