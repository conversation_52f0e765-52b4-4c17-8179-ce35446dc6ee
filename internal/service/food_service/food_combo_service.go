package food_service

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

// FoodComboService 菜品套餐业务逻辑
type FoodComboService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetMerchantFoodCombos 获取菜品套餐列表
func (serv *FoodComboService) GetMerchantFoodCombos(ctx context.Context, merchantNo string) ([]*model.FoodComboModel, error) {
	var list []*model.FoodComboModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// CreateComboFood 创建套餐
func (serv *FoodComboService) CreateComboFood(ctx context.Context, req *food_request.FoodComboRequest, merchantNo string) (*model.FoodModel, error) {
	// 验证套餐信息
	err := serv.validateCombo(ctx, req, merchantNo)
	if err != nil {
		return nil, err
	}

	// 检查分类是否存在且属于该商户
	var categoryCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND state = ?", req.FoodCategoryID, merchantNo, 1).
		Count(&categoryCount).Error
	if err != nil {
		return nil, err
	}
	if categoryCount == 0 {
		return nil, errors.BadRequest("", "FoodCategoryNotFound")
	}

	// 检查名称是否已存在
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND (name_ug = ? OR name_zh = ?)", merchantNo, req.NameUg, req.NameZh).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "FoodNameExists")
	}

	// 创建美食记录
	food := &model.FoodModel{
		MerchantNo:     merchantNo,
		FoodCategoryID: req.FoodCategoryID,
		Image:          req.Image,
		ShortcutCode:   req.ShortcutCode,
		NameUg:         req.NameUg,
		NameZh:         req.NameZh,
		CostPrice:      req.CostPrice,
		VipPrice:       req.VipPrice,
		Price:          req.Price,
		FormatID:       1, // 按数量销售
		IsSpecialFood:  req.IsSpecialFood,
		IsCombo:        true,
		Sort:           req.Sort,
		State:          req.State,
		IsSync:         false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		err = util.GetDB(ctx, serv.DB).Create(food).Error
		if err != nil {
			return err
		}
		err = serv.updateComboFoods(ctx, food.ID, merchantNo, req.ComboFoods)
		if err != nil {
			return err
		}
		return nil
	})

	return food, nil
}

// UpdateComboFood 更新套餐
func (serv *FoodComboService) UpdateComboFood(ctx context.Context, foodId int64, req *food_request.FoodComboRequest, merchantNo string) (*model.FoodModel, error) {
	// 验证套餐信息
	err := serv.validateCombo(ctx, req, merchantNo)
	if err != nil {
		return nil, err
	}

	// 检查美食是否存在且属于该商户
	var food model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ?", foodId, merchantNo), &food)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodNotFound")
	}

	if food.IsCombo == false {
		return nil, errors.BadRequest("", "FoodIsNotCombo")
	}

	// 检查分类是否存在且属于该商户
	var categoryCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND state = ?", req.FoodCategoryID, merchantNo, 1).
		Count(&categoryCount).Error
	if err != nil {
		return nil, err
	}
	if categoryCount == 0 {
		return nil, errors.BadRequest("", "FoodCategoryNotFound")
	}

	// 检查名称是否已存在（排除当前记录）
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND id <> ? AND (name_ug = ? OR name_zh = ?)", merchantNo, foodId, req.NameUg, req.NameZh).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "FoodNameExists")
	}

	// 更新美食记录
	food.FoodCategoryID = req.FoodCategoryID
	food.Image = req.Image
	food.ShortcutCode = req.ShortcutCode
	food.NameUg = req.NameUg
	food.NameZh = req.NameZh
	food.CostPrice = req.CostPrice
	food.VipPrice = req.VipPrice
	food.Price = req.Price
	food.FormatID = 1 // 按数量销售
	food.IsSpecialFood = req.IsSpecialFood
	food.SupportScanOrder = req.SupportScanOrder
	food.IsCombo = true
	food.Sort = req.Sort
	food.State = req.State
	food.UpdatedAt = time.Now()

	err = serv.Trans.Exec(ctx, func(ctx context.Context) error {
		err = util.GetDB(ctx, serv.DB).Save(&food).Error
		if err != nil {
			return err
		}
		err = serv.updateComboFoods(ctx, foodId, merchantNo, req.ComboFoods)
		if err != nil {
			return err
		}
		return nil
	})

	return &food, nil
}

// validateCombo 验证套餐信息
func (serv *FoodComboService) validateCombo(ctx context.Context, req *food_request.FoodComboRequest, merchantNo string) error {

	// 检查套餐菜品信息
	if len(req.ComboFoods) == 0 {
		return errors.BadRequest("", "FoodComboInfoCantBeNull")
	}

	// 获取所有美食ID
	var foodIds []int64
	totalComboPrice := 0.0

	for _, combo := range req.ComboFoods {
		totalComboPrice += combo.ComboPrice * float64(combo.ComboCount)

		for _, id := range combo.ComboFoodIds {
			foodIds = append(foodIds, id)
		}
	}

	// 检查所有菜品是否属于该商户
	for _, foodId := range foodIds {
		var food model.FoodModel
		err := util.GetDB(ctx, serv.DB).Where("id = ? AND merchant_no = ?", foodId, merchantNo).First(&food).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.BadRequest("", "FoodNotFound")
			}
			return err
		}
	}

	// 验证总价
	if fmt.Sprintf("%.2f", req.Price) != fmt.Sprintf("%.2f", totalComboPrice) {
		return errors.BadRequest("", fmt.Sprintf("FoodPriceAndComboPriceNotEqual: price=%.2f, combo_price=%.2f", req.Price, totalComboPrice))
	}

	return nil
}

// updateComboFoods 更新套餐菜品
func (serv *FoodComboService) updateComboFoods(ctx context.Context, foodId int64, merchantNo string, comboFoods []food_request.ComboFoodRequest) error {
	// 删除现有的套餐关联
	err := util.GetDB(ctx, serv.DB).Where("combo_id = ?", foodId).Delete(&model.FoodComboModel{}).Error
	if err != nil {
		return err
	}

	// 创建新的套餐关联
	for _, combo := range comboFoods {
		// 创建主套餐记录
		mainCombo := &model.FoodComboModel{
			MerchantNo:  merchantNo,
			ComboID:     foodId,
			Type:        "1", // 主套餐
			FoodID:      foodId,
			OriginPrice: 0, // 这里需要从food表获取原价
			ComboPrice:  combo.ComboPrice,
			Count:       combo.ComboCount,
			NameUg:      combo.NameUg,
			NameZh:      combo.NameZh,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		err = util.GetDB(ctx, serv.DB).Create(mainCombo).Error
		if err != nil {
			return err
		}

		// 解析逗号分隔的菜品ID
		for _, comboFoodId := range combo.ComboFoodIds {
			// 获取菜品信息
			var food model.FoodModel
			err = util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND id = ?", merchantNo, comboFoodId).First(&food).Error
			if err != nil {
				continue
			}

			// 创建子套餐记录
			childCombo := &model.FoodComboModel{
				MerchantNo:  merchantNo,
				ComboID:     mainCombo.ComboID,
				Type:        "2", // 子套餐
				ParentID:    mainCombo.ID,
				FoodID:      comboFoodId,
				OriginPrice: 0,
				ComboPrice:  0,
				NameUg:      food.NameUg,
				NameZh:      food.NameZh,
				Count:       0,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}

			err = util.GetDB(ctx, serv.DB).Create(childCombo).Error
			if err != nil {
				return err
			}
		}
	}

	return nil
}
