package food_service

import (
	"context"
	"ros-api-go/internal/http/request/food_request"
	"time"

	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

// MethodGroupService 做法分组服务
type MethodGroupService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetList 获取做法分组列表
func (serv *MethodGroupService) GetList(ctx context.Context, merchantNo string, req *food_request.MethodGroupListRequest) ([]*model.MethodGroupModel, error) {
	var methodGroups []*model.MethodGroupModel

	query := util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Select("method_groups.*, COALESCE(method_counts.methods_count, 0) as methods_count").
		Joins("LEFT JOIN (SELECT group_id, COUNT(*) as methods_count FROM methods WHERE merchant_no = ? GROUP BY group_id) as method_counts ON method_groups.id = method_counts.group_id", merchantNo).
		Where("method_groups.merchant_no = ?", merchantNo).
		Where("method_groups.state > ?", model.MethodGroupStateDeleted)

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("method_groups.name_zh LIKE ? OR method_groups.name_ug LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 按排序字段升序排列
	query = query.Order("method_groups.sort ASC, method_groups.id ASC")

	// 执行查询并处理分页
	err := query.Find(&methodGroups).Error

	return methodGroups, err
}

// GetByID 根据ID获取做法分组
func (serv *MethodGroupService) GetByID(ctx context.Context, merchantNo string, id int64) (*model.MethodGroupModel, error) {
	var methodGroup model.MethodGroupModel

	ok, err := util.FindOne(
		ctx,
		util.GetDB(ctx, serv.DB).Where("id = ? AND merchant_no = ? AND state > ?", id, merchantNo, model.MethodGroupStateDeleted),
		&methodGroup,
	)

	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, errors.NotFound("", "MethodGroupNotFound")
	}

	return &methodGroup, nil
}

// Create 创建做法分组
func (serv *MethodGroupService) Create(ctx context.Context, req *food_request.CreateMethodGroupRequest, merchantNo string) (*model.MethodGroupModel, error) {
	// 检查中文名称是否已存在
	var count int64
	err := util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("merchant_no = ? AND name_zh = ? AND state > ?", merchantNo, req.NameZh, model.MethodGroupStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodGroupNameExists")
	}

	// 检查维语名称是否已存在
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("merchant_no = ? AND name_ug = ? AND state > ?", merchantNo, req.NameUg, model.MethodGroupStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodGroupNameExists")
	}

	// 获取当前最大排序值
	var maxSort int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("merchant_no = ?", merchantNo).
		Select("COALESCE(MAX(sort), 0)").
		Scan(&maxSort).Error
	if err != nil {
		return nil, err
	}

	// 创建做法分组记录
	methodGroup := &model.MethodGroupModel{
		MerchantNo: merchantNo,
		NameZh:     req.NameZh,
		NameUg:     req.NameUg,
		Sort:       maxSort + 1,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err = util.GetDB(ctx, serv.DB).Create(methodGroup).Error
	if err != nil {
		return nil, err
	}

	return methodGroup, nil
}

// Update 更新做法分组
func (serv *MethodGroupService) Update(ctx context.Context, id int64, req *food_request.UpdateMethodGroupRequest, merchantNo string) (*model.MethodGroupModel, error) {
	// 检查做法分组是否存在
	methodGroup, err := serv.GetByID(ctx, merchantNo, id)
	if err != nil {
		return nil, err
	}

	// 检查中文名称是否已被其他记录使用
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("merchant_no = ? AND name_zh = ? AND id != ? AND state > ?", merchantNo, req.NameZh, id, model.MethodGroupStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodGroupNameExists")
	}

	// 检查维语名称是否已被其他记录使用
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("merchant_no = ? AND name_ug = ? AND id != ? AND state > ?", merchantNo, req.NameUg, id, model.MethodGroupStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodGroupNameExists")
	}

	// 更新做法分组
	methodGroup.NameZh = req.NameZh
	methodGroup.NameUg = req.NameUg
	methodGroup.UpdatedAt = time.Now()

	err = util.GetDB(ctx, serv.DB).Save(methodGroup).Error
	if err != nil {
		return nil, err
	}

	return methodGroup, nil
}

// Delete 删除做法分组
func (serv *MethodGroupService) Delete(ctx context.Context, id int64, merchantNo string) error {
	// 检查做法分组是否存在
	_, err := serv.GetByID(ctx, merchantNo, id)
	if err != nil {
		return err
	}

	// 检查分组下是否有做法
	var methodCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Where("group_id = ? AND merchant_no = ?", id, merchantNo).
		Count(&methodCount).Error
	if err != nil {
		return err
	}
	if methodCount > 0 {
		return errors.BadRequest("", "MethodGroupHasMethods")
	}

	// 删除做法分组
	now := time.Now()
	err = util.GetDB(ctx, serv.DB).
		Model(&model.MethodGroupModel{}).
		Where("id = ? AND merchant_no = ?", id, merchantNo).
		Updates(map[string]interface{}{
			"state":      model.MethodGroupStateDeleted,
			"deleted_at": &now,
		}).Error
	if err != nil {
		return err
	}

	return nil
}

// SaveSort 保存做法分组排序
func (serv *MethodGroupService) SaveSort(ctx context.Context, req *food_request.SaveMethodGroupSortRequest, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 批量验证做法分组是否属于当前商家
		groupIDs := make([]int64, 0, len(req.Items))
		for _, item := range req.Items {
			groupIDs = append(groupIDs, item.ID)
		}

		var count int64
		err := util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
			Where("id IN ? AND merchant_no = ?", groupIDs, merchantNo).
			Count(&count).Error
		if err != nil {
			return err
		}
		if count != int64(len(groupIDs)) {
			return errors.BadRequest("", "MethodGroupNotFound")
		}

		// 批量更新排序
		for _, item := range req.Items {
			err := util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
