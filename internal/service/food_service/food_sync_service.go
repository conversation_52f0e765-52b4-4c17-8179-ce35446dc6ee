package food_service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type FoodSyncService struct {
	DB *gorm.DB
}

// GetMerchantFoodsForSync 获取商户的美食列表（用于同步）
func (serv *FoodSyncService) GetMerchantFoodsForSync(ctx context.Context, merchantNo string) ([]*model.FoodModel, error) {
	var list []*model.FoodModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =? AND deleted_at IS NULL", merchantNo).Find(&list).Error
}

// GetMerchantFoodCategoriesForSync 获取商户菜品分类列表
func (serv *FoodSyncService) GetMerchantFoodCategoriesForSync(ctx context.Context, merchantNo string) ([]*model.FoodCategoryModel, error) {
	var list []*model.FoodCategoryModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =? AND deleted_at IS NULL", merchantNo).Find(&list).Error
}

// GetMerchantFoodCombosForSync 获取菜品套餐列表
func (serv *FoodSyncService) GetMerchantFoodCombosForSync(ctx context.Context, merchantNo string) ([]*model.FoodComboModel, error) {
	var list []*model.FoodComboModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// GetMerchantMethodGroupsForSync 获取菜品做法分组列表
func (serv *FoodSyncService) GetMerchantMethodGroupsForSync(ctx context.Context, merchantNo string) ([]*model.MethodGroupModel, error) {
	var list []*model.MethodGroupModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// GetMerchantMethodsForSync 获取菜品做法列表
func (serv *FoodSyncService) GetMerchantMethodsForSync(ctx context.Context, merchantNo string) ([]*model.MethodModel, error) {
	var list []*model.MethodModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// GetMerchantFoodMethodForSync 获取菜品做法关联列表
func (serv *FoodSyncService) GetMerchantFoodMethodForSync(ctx context.Context, merchantNo string) ([]*model.FoodMethodModel, error) {
	var list []*model.FoodMethodModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// GetMerchantFoodAdditionForSync 获取菜品加料关联列表
func (serv *FoodSyncService) GetMerchantFoodAdditionForSync(ctx context.Context, merchantNo string) ([]*model.FoodAdditionModel, error) {
	var list []*model.FoodAdditionModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// GetMerchantFoodLunchBoxForSync 获取菜品餐盒关联列表
func (serv *FoodSyncService) GetMerchantFoodLunchBoxForSync(ctx context.Context, merchantNo string) ([]*model.FoodLunchBoxModel, error) {
	var list []*model.FoodLunchBoxModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// UpdateFoodSellClearData 更新菜品沽清数据业务逻辑
func (serv *FoodSyncService) UpdateFoodSellClearData(ctx context.Context, formItem food_request.FoodSellClearDataRequest) error {
	food := model.FoodModel{}
	err := util.GetDB(ctx, serv.DB).
		Where("merchant_no =? and id =?", formItem.MerchantNo, formItem.ID).
		First(&food).Error
	if err != nil {
		return err
	}
	food.CellClearState = formItem.CellClearState
	food.SellClearCount = formItem.SellClearCount
	food.RemainingCount = formItem.RemainingCount
	return util.GetDB(ctx, serv.DB).
		Select("cell_clear_state", "sell_clear_count", "remaining_count").
		Save(&food).Error
}
