package food_service

import (
	"context"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// FoodAdditionService 加料业务逻辑
type FoodAdditionService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetFoodAdditionList 获取加料列表（分页）
func (serv *FoodAdditionService) GetFoodAdditionList(ctx context.Context, req *food_request.FoodAdditionListRequest, merchantNo string) ([]*model.FoodModel, error) {
	query := util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND type = ? AND deleted_at IS NULL", merchantNo, model.FoodTypeAddition). // type=2 表示加料
		Preload("FoodCategory").
		Select("foods.*, (SELECT COUNT(*) FROM food_addition WHERE addition_id = foods.id) as associated_foods")

	// 按分类筛选
	if req.CategoryID > 0 {
		query = query.Where("food_category_id = ?", req.CategoryID)
	}

	// 按状态筛选
	if req.State != nil && *req.State >= 0 {
		query = query.Where("state = ?", *req.State)
	}

	// 搜索关键词
	if req.Keyword != "" {
		query = query.Where("(name_zh LIKE ? OR name_ug LIKE ?)", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 按排序字段排序
	query = query.Order("sort ASC, id DESC")

	var list []*model.FoodModel

	return list, query.Find(&list).Error
}

// CreateFoodAddition 创建加料
func (serv *FoodAdditionService) CreateFoodAddition(ctx context.Context, req *food_request.CreateFoodAdditionRequest, merchantNo string) (*model.FoodModel, error) {
	// 检查分类是否存在且属于该商户，且为加料分类
	var categoryCount int64
	err := util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND type = ? AND state = ?", req.FoodCategoryID, merchantNo, model.FoodCategoryTypeAddition, model.FoodCategoryStateNormal).
		Count(&categoryCount).Error
	if err != nil {
		return nil, err
	}
	if categoryCount == 0 {
		return nil, errors.BadRequest("", "AdditionCategoryNotFound")
	}

	// 检查名称是否已存在
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND type = ? AND (name_ug = ? OR name_zh = ?) AND deleted_at IS NULL", merchantNo, model.FoodTypeAddition, req.NameUg, req.NameZh).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "AdditionNameExists")
	}

	// 如果没有提供排序值，使用数据库默认值
	sort := req.Sort
	if sort == 0 {
		var maxSort int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("merchant_no = ? AND type = ? AND deleted_at IS NULL", merchantNo, model.FoodTypeAddition).
			Select("COALESCE(MAX(sort), 0)").Scan(&maxSort).Error
		if err != nil {
			return nil, err
		}
		sort = maxSort + 1
	}

	// 创建加料记录
	addition := &model.FoodModel{
		MerchantNo:       merchantNo,
		Type:             model.FoodTypeAddition, // 加料类型
		FoodCategoryID:   req.FoodCategoryID,
		Image:            req.Image,
		ShortcutCode:     req.ShortcutCode,
		NameUg:           req.NameUg,
		NameZh:           req.NameZh,
		CostPrice:        req.CostPrice,
		VipPrice:         req.VipPrice,
		Price:            req.Price,
		FormatID:         req.FormatID,
		IsSpecialFood:    req.IsSpecialFood,
		SupportScanOrder: req.SupportScanOrder,
		Sort:             sort,
		State:            req.State,
		IsSync:           false,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = util.GetDB(ctx, serv.DB).Create(addition).Error
	if err != nil {
		return nil, err
	}

	return addition, nil
}

// UpdateFoodAddition 更新加料
func (serv *FoodAdditionService) UpdateFoodAddition(ctx context.Context, additionID int64, req *food_request.UpdateFoodAdditionRequest, merchantNo string) (*model.FoodModel, error) {
	// 检查加料是否存在且属于该商户
	var addition model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", additionID, merchantNo, model.FoodTypeAddition), &addition)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "AdditionNotFound")
	}

	// 检查分类是否存在且属于该商户，且为加料分类
	var categoryCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ? AND type = ? AND state = ?", req.FoodCategoryID, merchantNo, model.FoodCategoryTypeAddition, model.FoodCategoryStateNormal).
		Count(&categoryCount).Error
	if err != nil {
		return nil, err
	}
	if categoryCount == 0 {
		return nil, errors.BadRequest("", "AdditionCategoryNotFound")
	}

	// 检查名称是否已存在（排除当前记录）
	var nameCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND type = ? AND (name_ug = ? OR name_zh = ?) AND id != ? AND deleted_at IS NULL", merchantNo, model.FoodTypeAddition, req.NameUg, req.NameZh, additionID).
		Count(&nameCount).Error
	if err != nil {
		return nil, err
	}
	if nameCount > 0 {
		return nil, errors.BadRequest("", "AdditionNameExists")
	}

	// 更新加料信息
	addition.FoodCategoryID = req.FoodCategoryID
	addition.Image = req.Image
	addition.ShortcutCode = req.ShortcutCode
	addition.NameUg = req.NameUg
	addition.NameZh = req.NameZh
	addition.CostPrice = req.CostPrice
	addition.VipPrice = req.VipPrice
	addition.Price = req.Price
	addition.FormatID = req.FormatID
	addition.IsSpecialFood = req.IsSpecialFood
	addition.SupportScanOrder = req.SupportScanOrder
	addition.Sort = req.Sort
	addition.State = req.State
	addition.UpdatedAt = time.Now()

	err = util.GetDB(ctx, serv.DB).Save(&addition).Error
	if err != nil {
		return nil, err
	}

	return &addition, nil
}

// DeleteFoodAddition 删除加料
func (serv *FoodAdditionService) DeleteFoodAddition(ctx context.Context, additionID int64, merchantNo string) error {
	// 检查加料是否存在且属于该商户
	var addition model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", additionID, merchantNo, model.FoodTypeAddition), &addition)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "AdditionNotFound")
	}

	// 检查是否有关联的菜品
	var associationCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodAdditionModel{}).
		Where("addition_id = ? AND merchant_no = ?", additionID, merchantNo).
		Count(&associationCount).Error
	if err != nil {
		return err
	}
	if associationCount > 0 {
		return errors.BadRequest("", "AdditionHasAssociatedFoods")
	}

	// 软删除加料
	now := time.Now()
	err = util.GetDB(ctx, serv.DB).Model(&addition).Update("deleted_at", &now).Error
	if err != nil {
		return err
	}

	return nil
}

// GetAdditionFoods 获取加料关联的美食列表
func (serv *FoodAdditionService) GetAdditionFoods(ctx context.Context, additionID int64, req *food_request.GetFoodAdditionFoodsRequest, merchantNo string) (*model.FoodModel, []*model.FoodAdditionModel, error) {
	// 检查加料是否存在
	var addition model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", additionID, merchantNo, model.FoodTypeAddition), &addition)
	if err != nil {
		return nil, nil, err
	}
	if !one {
		return nil, nil, errors.NotFound("", "AdditionNotFound")
	}

	query := util.GetDB(ctx, serv.DB).Model(&model.FoodAdditionModel{}).
		Where("addition_id = ? AND merchant_no = ?", additionID, merchantNo).
		Preload("Food.FoodCategory").
		Order("created_at DESC")

	var list []*model.FoodAdditionModel

	return &addition, list, query.Find(&list).Error
}

// SaveFoodAdditionFoods 关联菜品
func (serv *FoodAdditionService) SaveFoodAdditionFoods(ctx context.Context, req *food_request.SaveFoodAdditionFoodsRequest, merchantNo string) error {
	// 检查加料是否存在
	var addition model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", req.AdditionID, merchantNo, model.FoodTypeAddition), &addition)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "AdditionNotFound")
	}

	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 提取所有食品ID用于批量查询
		foodIDs := make([]int64, len(req.Foods))
		for i, foodItem := range req.Foods {
			foodIDs[i] = foodItem.FoodID
		}

		// 批量检查菜品是否存在
		var existingFoods []model.FoodModel
		err := util.GetDB(ctx, serv.DB).
			Where("id IN ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", foodIDs, merchantNo, 1).
			Find(&existingFoods).Error
		if err != nil {
			return err
		}

		// 创建存在的菜品ID映射
		existingFoodMap := make(map[int64]bool)
		for _, food := range existingFoods {
			existingFoodMap[food.ID] = true
		}

		// 验证所有菜品都存在
		for _, foodItem := range req.Foods {
			if !existingFoodMap[foodItem.FoodID] {
				return errors.BadRequest("", "FoodNotFound")
			}
		}

		// 批量查询现有关联关系
		var existingAssociations []model.FoodAdditionModel
		err = util.GetDB(ctx, serv.DB).
			Where("food_id IN ? AND addition_id = ? AND category_id = ? AND merchant_no = ?",
				foodIDs, req.AdditionID, addition.FoodCategoryID, merchantNo).
			Find(&existingAssociations).Error
		if err != nil {
			return err
		}

		// 创建现有关联关系映射
		existingAssociationMap := make(map[int64]*model.FoodAdditionModel)
		for i := range existingAssociations {
			existingAssociationMap[existingAssociations[i].FoodID] = &existingAssociations[i]
		}

		// 准备批量操作的数据
		var toCreate []*model.FoodAdditionModel
		var toUpdateRecords []*model.FoodAdditionModel
		now := time.Now()

		for _, foodItem := range req.Foods {
			if existing, exists := existingAssociationMap[foodItem.FoodID]; exists {
				existing.UpdatedAt = now
				toUpdateRecords = append(toUpdateRecords, existing)
			} else {
				// 需要创建的记录
				toCreate = append(toCreate, &model.FoodAdditionModel{
					MerchantNo: merchantNo,
					FoodID:     foodItem.FoodID,
					CategoryID: addition.FoodCategoryID,
					AdditionID: req.AdditionID,
					CreatedAt:  now,
					UpdatedAt:  now,
				})
			}
		}

		// 批量创建新关联关系
		if len(toCreate) > 0 {
			err = util.GetDB(ctx, serv.DB).CreateInBatches(toCreate, 100).Error
			if err != nil {
				return err
			}
		}

		// 批量更新现有关联关系
		if len(toUpdateRecords) > 0 {
			// 使用GORM的批量保存功能
			err = util.GetDB(ctx, serv.DB).Save(toUpdateRecords).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// RemoveFoodAdditionFoods 解除关联
func (serv *FoodAdditionService) RemoveFoodAdditionFoods(ctx context.Context, req *food_request.RemoveFoodAdditionFoodsRequest, merchantNo string) error {
	// 检查加料是否存在
	var addition model.FoodModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND type = ? AND deleted_at IS NULL", req.AdditionID, merchantNo, model.FoodTypeAddition), &addition)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "AdditionNotFound")
	}

	// 删除关联关系
	err = util.GetDB(ctx, serv.DB).
		Where("addition_id = ? AND food_id IN ? AND merchant_no = ?", req.AdditionID, req.FoodIDs, merchantNo).
		Delete(&model.FoodAdditionModel{}).Error
	if err != nil {
		return err
	}

	return nil
}
