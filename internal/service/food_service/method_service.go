package food_service

import (
	"context"
	"ros-api-go/internal/http/request/food_request"
	"time"

	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

// MethodService 做法服务
type MethodService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetList 获取做法列表
func (serv *MethodService) GetList(ctx context.Context, merchantNo string, req *food_request.MethodListRequest) ([]*model.MethodModel, error) {
	var methods []*model.MethodModel

	query := util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Select("methods.*, COALESCE(food_counts.foods_count, 0) as foods_count").
		Joins("LEFT JOIN (SELECT method_id, COUNT(*) as foods_count FROM food_method WHERE merchant_no = ? GROUP BY method_id) as food_counts ON methods.id = food_counts.method_id", merchantNo).
		Where("methods.merchant_no = ?", merchantNo).
		Where("methods.state > ?", model.MethodStateDeleted).
		Preload("Group") // 预加载分组信息

	// 分组过滤
	if req.GroupID > 0 {
		query = query.Where("methods.group_id = ?", req.GroupID)
	}

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("methods.name_zh LIKE ? OR methods.name_ug LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 按排序字段升序排列
	query = query.Order("methods.sort ASC, methods.id ASC")

	// 执行查询并处理分页
	err := query.Find(&methods).Error

	return methods, err
}

// GetByID 根据ID获取做法
func (serv *MethodService) GetByID(ctx context.Context, merchantNo string, id int64) (*model.MethodModel, error) {
	var method model.MethodModel

	ok, err := util.FindOne(
		ctx,
		util.GetDB(ctx, serv.DB).Where("id = ? AND merchant_no = ? AND state > ?", id, merchantNo, model.MethodStateDeleted).
			Preload("Group"),
		&method,
	)

	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, errors.NotFound("", "MethodNotFound")
	}

	return &method, nil
}

// Create 创建做法
func (serv *MethodService) Create(ctx context.Context, req *food_request.CreateMethodRequest, merchantNo string) (*model.MethodModel, error) {
	// 验证分组是否存在
	var groupCount int64
	err := util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("id = ? AND merchant_no = ?", req.GroupID, merchantNo).
		Count(&groupCount).Error
	if err != nil {
		return nil, err
	}
	if groupCount == 0 {
		return nil, errors.BadRequest("", "MethodGroupNotFound")
	}

	// 检查中文名称是否已存在
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Where("merchant_no = ? AND name_zh = ? AND state > ?", merchantNo, req.NameZh, model.MethodStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodNameExists")
	}

	// 检查维语名称是否已存在
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Where("merchant_no = ? AND name_ug = ? AND state > ?", merchantNo, req.NameUg, model.MethodStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodNameExists")
	}

	// 获取当前最大排序值
	var maxSort int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Where("merchant_no = ?", merchantNo).
		Select("COALESCE(MAX(sort), 0)").
		Scan(&maxSort).Error
	if err != nil {
		return nil, err
	}

	// 创建做法记录
	method := &model.MethodModel{
		MerchantNo: merchantNo,
		GroupID:    req.GroupID,
		NameZh:     req.NameZh,
		NameUg:     req.NameUg,
		DescZh:     req.DescZh,
		DescUg:     req.DescUg,
		Price:      req.Price,
		Sort:       maxSort + 1,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err = util.GetDB(ctx, serv.DB).Create(method).Error
	if err != nil {
		return nil, err
	}

	// 重新查询以获取关联数据
	return serv.GetByID(ctx, merchantNo, method.ID)
}

// Update 更新做法
func (serv *MethodService) Update(ctx context.Context, id int64, req *food_request.UpdateMethodRequest, merchantNo string) (*model.MethodModel, error) {
	// 检查做法是否存在
	method, err := serv.GetByID(ctx, merchantNo, id)
	if err != nil {
		return nil, err
	}

	// 验证分组是否存在
	var groupCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodGroupModel{}).
		Where("id = ? AND merchant_no = ?", req.GroupID, merchantNo).
		Count(&groupCount).Error
	if err != nil {
		return nil, err
	}
	if groupCount == 0 {
		return nil, errors.BadRequest("", "MethodGroupNotFound")
	}

	// 检查中文名称是否已被其他记录使用
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Where("merchant_no = ? AND name_zh = ? AND id != ? AND state > ?", merchantNo, req.NameZh, id, model.MethodStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodNameExists")
	}

	// 检查维语名称是否已被其他记录使用
	err = util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
		Where("merchant_no = ? AND name_ug = ? AND id != ? AND state > ?", merchantNo, req.NameUg, id, model.MethodStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "MethodNameExists")
	}

	// 更新做法
	method.GroupID = req.GroupID
	method.NameZh = req.NameZh
	method.NameUg = req.NameUg
	method.DescZh = req.DescZh
	method.DescUg = req.DescUg
	method.Price = req.Price
	method.UpdatedAt = time.Now()

	err = util.GetDB(ctx, serv.DB).Save(method).Error
	if err != nil {
		return nil, err
	}

	// 重新查询以获取关联数据
	return serv.GetByID(ctx, merchantNo, method.ID)
}

// Delete 删除做法
func (serv *MethodService) Delete(ctx context.Context, id int64, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 检查做法是否存在
		_, err := serv.GetByID(ctx, merchantNo, id)
		if err != nil {
			return err
		}

		// 检查是否有关联的美食
		var foodCount int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodMethodModel{}).
			Where("method_id = ? AND merchant_no = ?", id, merchantNo).
			Count(&foodCount).Error
		if err != nil {
			return err
		}
		if foodCount > 0 {
			return errors.BadRequest("", "MethodHasFoods")
		}

		// 删除做法
		now := time.Now()
		err = util.GetDB(ctx, serv.DB).
			Model(&model.MethodModel{}).
			Where("id = ? AND merchant_no = ?", id, merchantNo).
			Updates(map[string]interface{}{
				"state":      model.MethodStateDeleted,
				"deleted_at": &now,
			}).Error
		if err != nil {
			return err
		}

		return nil
	})
}

// SaveSort 保存做法排序
func (serv *MethodService) SaveSort(ctx context.Context, req *food_request.SaveMethodSortRequest, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 批量验证做法是否属于当前商家
		methodIDs := make([]int64, 0, len(req.Items))
		for _, item := range req.Items {
			methodIDs = append(methodIDs, item.ID)
		}

		var count int64
		err := util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
			Where("id IN ? AND merchant_no = ?", methodIDs, merchantNo).
			Count(&count).Error
		if err != nil {
			return err
		}
		if count != int64(len(methodIDs)) {
			return errors.BadRequest("", "MethodNotFound")
		}

		// 批量更新排序
		for _, item := range req.Items {
			err := util.GetDB(ctx, serv.DB).Model(&model.MethodModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// GetMethodFoods 获取做法关联的美食列表
func (serv *MethodService) GetMethodFoods(ctx context.Context, merchantNo string, methodID int64, req *food_request.MethodFoodListRequest) (*model.MethodModel, []*model.FoodMethodModel, error) {

	// 检查加料是否存在
	method, err := serv.GetByID(ctx, merchantNo, methodID)
	if err != nil {
		return nil, nil, err
	}

	var foodMethods []*model.FoodMethodModel

	query := util.GetDB(ctx, serv.DB).Model(&model.FoodMethodModel{}).
		Where("food_method.merchant_no = ? AND method_id = ?", merchantNo, methodID).
		Preload("Food.FoodCategory").
		Preload("Group").
		Preload("Method")

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Joins("JOIN foods ON food_method.food_id = foods.id").
			Where("foods.name_zh LIKE ? OR foods.name_ug LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 按美食排序字段排序
	query = query.Joins("JOIN foods ON food_method.food_id = foods.id").
		Order("foods.sort ASC, food_method.id ASC")

	// 执行查询
	return method, foodMethods, query.Find(&foodMethods).Error
}

// SaveFoodMethod 保存美食做法关联
func (serv *MethodService) SaveFoodMethod(ctx context.Context, req *food_request.SaveFoodMethodRequest, merchantNo string) error {
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 验证做法是否存在
		_, err := serv.GetByID(ctx, merchantNo, req.MethodID)
		if err != nil {
			return err
		}

		// 删除现有关联关系
		err = util.GetDB(ctx, serv.DB).
			Where("method_id = ? AND merchant_no = ?", req.MethodID, merchantNo).
			Delete(&model.FoodMethodModel{}).Error
		if err != nil {
			return err
		}

		// 如果没有新的关联关系，直接返回（清空关联）
		if len(req.Foods) == 0 {
			return nil
		}

		// 验证所有美食是否存在
		foodIDs := make([]int64, 0, len(req.Foods))
		for _, food := range req.Foods {
			foodIDs = append(foodIDs, food.FoodID)
		}

		var count int64
		err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
			Where("id IN ? AND merchant_no = ? AND deleted_at IS NULL", foodIDs, merchantNo).
			Count(&count).Error
		if err != nil {
			return err
		}
		if count != int64(len(foodIDs)) {
			return errors.BadRequest("", "FoodNotFound")
		}

		// 获取做法默认价格
		method, err := serv.GetByID(ctx, merchantNo, req.MethodID)
		if err != nil {
			return err
		}

		// 创建新的关联关系
		for _, food := range req.Foods {
			price := food.Price
			if price == 0 {
				price = method.Price // 使用做法默认价格
			}

			foodMethod := &model.FoodMethodModel{
				MerchantNo: merchantNo,
				FoodID:     food.FoodID,
				GroupID:    method.GroupID,
				MethodID:   req.MethodID,
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}

			err = util.GetDB(ctx, serv.DB).Create(foodMethod).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}
