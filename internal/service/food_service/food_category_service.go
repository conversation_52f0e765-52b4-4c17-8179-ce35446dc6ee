package food_service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

// FoodCategoryService 菜品分类业务逻辑
type FoodCategoryService struct {
	DB *gorm.DB
}

// GetAvailableFoodCategories 获取商户菜品分类列表(正常)
func (serv *FoodCategoryService) GetAvailableFoodCategories(ctx context.Context, merchantNo string, categoryType int64) ([]*model.FoodCategoryModel, error) {
	var list []*model.FoodCategoryModel
	return list, util.GetDB(ctx, serv.DB).
		Select("food_categories.*,"+
			"(select count(*) from foods where foods.food_category_id = food_categories.id and foods.state = 1) as food_count").
		Where("merchant_no =? AND type =? AND state = ?", merchantNo, categoryType, model.FoodCategoryStateNormal).
		Having("food_count > 0").
		Order("type asc, sort asc").
		Find(&list).Error
}

// GetList 获取菜品分类列表（分页）
func (serv *FoodCategoryService) GetList(ctx context.Context, req *food_request.FoodCategoryListRequest) ([]*model.FoodCategoryModel, error) {
	var list []*model.FoodCategoryModel

	db := util.GetDB(ctx, serv.DB).
		Select("food_categories.*,"+
			"(select count(*) from foods where foods.food_category_id = food_categories.id) as foods_count").
		Model(&model.FoodCategoryModel{}).
		Where("state > ?", model.FoodCategoryStateDeleted).
		Order("type asc, sort asc")

	if req.MerchantNo != "" {
		db = db.Where("merchant_no = ?", req.MerchantNo)
	}

	if req.Type != nil && *req.Type > 0 {
		db = db.Where("type = ?", *req.Type)
	}

	if req.State != nil && *req.State >= 0 {
		db = db.Where("state = ?", *req.State)
	}

	return list, db.Find(&list).Error
}

// GetByID 根据ID获取菜品分类
func (serv *FoodCategoryService) GetByID(ctx context.Context, id int64, merchantNo string) (*model.FoodCategoryModel, error) {
	var category model.FoodCategoryModel

	db := util.GetDB(ctx, serv.DB).
		Select("food_categories.*,"+
			"(select count(*) from foods where foods.food_category_id = food_categories.id and foods.state = 1) as food_count").
		Where("id = ? AND merchant_no = ? AND state > ?", id, merchantNo, model.FoodCategoryStateDeleted)

	ok, err := util.FindOne(ctx, db, &category)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, errors.NotFound("", "FoodCategoryNotFound")
	}

	return &category, nil
}

// Create 创建菜品分类
func (serv *FoodCategoryService) Create(ctx context.Context, req *food_request.CreateFoodCategoryRequest, merchantNo string) (*model.FoodCategoryModel, error) {
	category := &model.FoodCategoryModel{
		MerchantNo: merchantNo,
		Type:       req.Type,
		NameUg:     req.NameUg,
		NameZh:     req.NameZh,
		Sort:       req.Sort,
		State:      req.State,
	}

	// 检查名称是否已存在
	var count int64
	err := util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("merchant_no = ? AND  (name_zh = ? OR name_ug = ?) AND state > ?", merchantNo, req.NameZh, req.NameUg, model.FoodCategoryStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "CategoryNameExists")
	}

	err = util.GetDB(ctx, serv.DB).Create(category).Error
	if err != nil {
		return nil, err
	}

	return category, nil
}

// Update 更新菜品分类
func (serv *FoodCategoryService) Update(ctx context.Context, id int64, req *food_request.UpdateFoodCategoryRequest, merchantNo string) (*model.FoodCategoryModel, error) {
	// 先检查是否存在
	category, err := serv.GetByID(ctx, id, merchantNo)
	if err != nil {
		return nil, err
	}

	// 检查名称是否已存在
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodCategoryModel{}).
		Where("merchant_no = ? AND  id <> ? AND (name_zh = ? OR name_ug = ?) AND state > ?", merchantNo, id, req.NameZh, req.NameUg, model.FoodCategoryStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "CategoryNameExists")
	}

	// 更新字段
	category.NameUg = req.NameUg
	category.NameZh = req.NameZh
	category.Sort = req.Sort
	category.State = req.State
	category.UpdatedAt = time.Now()

	// 保存更新
	err = util.GetDB(ctx, serv.DB).Save(category).Error
	if err != nil {
		return nil, err
	}

	return category, nil
}

// Delete 删除菜品分类（软删除）
func (serv *FoodCategoryService) Delete(ctx context.Context, id int64, merchantNo string) error {
	// 先检查是否存在
	foodCategory, err := serv.GetByID(ctx, id, merchantNo)
	if err != nil {
		return err
	}

	// 检查是否有关联的菜品
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("food_category_id = ? AND state > ?", id, model.FoodCategoryStateDeleted).
		Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		switch foodCategory.Type {
		case model.FoodCategoryTypeFood:
			return errors.BadRequest("", "CategoryHasActiveFood")
		case model.FoodCategoryTypeAddition:
			return errors.BadRequest("", "CategoryHasActiveAddition")
		case model.FoodCategoryTypeLunchBox:
			return errors.BadRequest("", "CategoryHasActiveLunchBox")
		}
	}

	// 软删除
	now := time.Now()
	return util.GetDB(ctx, serv.DB).
		Model(&model.FoodCategoryModel{}).
		Where("id = ? AND merchant_no = ?", id, merchantNo).
		Updates(map[string]interface{}{
			"state":      model.FoodCategoryStateDeleted,
			"updated_at": now,
			"deleted_at": &now,
		}).Error
}

// UpdateState 更新菜品分类状态
func (serv *FoodCategoryService) UpdateState(ctx context.Context, id int64, merchantNo string) (*model.FoodCategoryModel, error) {
	// 先检查是否存在
	category, err := serv.GetByID(ctx, id, merchantNo)
	if err != nil {
		return nil, err
	}

	// 更新状态
	if category.State == 1 {
		category.State = 0
	} else {
		category.State = 1
	}
	// 保存更新
	err = util.GetDB(ctx, serv.DB).Save(category).Error
	if err != nil {
		return nil, err
	}

	return category, nil
}

// SaveSort 保存美食分类排序
func (serv *FoodCategoryService) SaveSort(ctx context.Context, req *food_request.SaveFoodCategorySortRequest, merchantNo string) error {
	// 开启事务
	return util.GetDB(ctx, serv.DB).Transaction(func(tx *gorm.DB) error {
		// 批量验证分类是否属于当前商家
		categoryIDs := make([]int64, 0, len(req.Items))
		for _, item := range req.Items {
			categoryIDs = append(categoryIDs, item.ID)
		}

		// 批量更新排序
		for _, item := range req.Items {
			err := tx.Model(&model.FoodCategoryModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
