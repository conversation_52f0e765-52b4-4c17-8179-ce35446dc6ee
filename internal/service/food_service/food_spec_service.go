package food_service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

// FoodSpecService 规格业务逻辑
type FoodSpecService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetList 获取规格列表(不分页，显示关联美食数量)
func (serv *FoodSpecService) GetList(ctx context.Context, merchantNo string, req *food_request.FoodSpecListRequest) ([]*model.FoodSpecModel, error) {
	var list []*model.FoodSpecModel

	db := util.GetDB(ctx, serv.DB).
		Select("food_specs.*, (SELECT COUNT(*) FROM foods WHERE foods.spec_id = food_specs.id AND foods.deleted_at IS NULL) as foods_count").
		Where("merchant_no = ? AND state > ?", merchantNo, model.FoodSpecStateDeleted)

	// 添加搜索条件
	if req != nil && req.Keyword != "" {
		searchPattern := "%" + req.Keyword + "%"
		db = db.Where("(name_zh LIKE ? OR name_ug LIKE ?)", searchPattern, searchPattern)
	}

	return list, db.Order("sort ASC, id ASC").Find(&list).Error
}

// GetByID 根据ID获取规格
func (serv *FoodSpecService) GetByID(ctx context.Context, id int64, merchantNo string) (*model.FoodSpecModel, error) {
	var spec model.FoodSpecModel

	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND state > ?", id, merchantNo, model.FoodSpecStateDeleted), &spec)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodSpecNotFound")
	}

	return &spec, nil
}

// Create 创建规格
func (serv *FoodSpecService) Create(ctx context.Context, req *food_request.CreateFoodSpecRequest, merchantNo string) (*model.FoodSpecModel, error) {
	// 检查名称是否已存在
	var count int64
	err := util.GetDB(ctx, serv.DB).Model(&model.FoodSpecModel{}).
		Where("merchant_no = ? AND (name_zh = ? OR name_ug = ?) AND state > ?", merchantNo, req.NameZh, req.NameUg, model.FoodSpecStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "FoodSpecNameExists")
	}

	// 创建规格记录
	spec := &model.FoodSpecModel{
		MerchantNo: merchantNo,
		NameZh:     req.NameZh,
		NameUg:     req.NameUg,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err = util.GetDB(ctx, serv.DB).Create(spec).Error
	if err != nil {
		return nil, err
	}

	return spec, nil
}

// Update 更新规格
func (serv *FoodSpecService) Update(ctx context.Context, id int64, req *food_request.UpdateFoodSpecRequest, merchantNo string) (*model.FoodSpecModel, error) {
	var spec model.FoodSpecModel

	// 查询规格记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND state > ?", id, merchantNo, model.FoodSpecStateDeleted), &spec)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodSpecNotFound")
	}

	if spec.IsDefault {
		return nil, errors.BadRequest("", "FoodSpecIsDefault")
	}

	// 检查名称是否已存在（排除当前记录）
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodSpecModel{}).
		Where("merchant_no = ? AND id <> ? AND (name_zh = ? OR name_ug = ?) AND state > ?", merchantNo, id, req.NameZh, req.NameUg, model.FoodSpecStateDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "FoodSpecNameExists")
	}

	// 更新规格信息
	spec.NameZh = req.NameZh
	spec.NameUg = req.NameUg
	spec.UpdatedAt = time.Now()

	err = util.GetDB(ctx, serv.DB).Save(&spec).Error
	if err != nil {
		return nil, err
	}

	return &spec, nil
}

// Delete 删除规格
func (serv *FoodSpecService) Delete(ctx context.Context, id int64, merchantNo string) error {
	var spec model.FoodSpecModel

	// 查询规格记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND state > ?", id, merchantNo, model.FoodSpecStateDeleted), &spec)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodSpecNotFound")
	}

	if spec.IsDefault {
		return errors.BadRequest("", "FoodSpecIsDefault")
	}

	// 检查是否有关联的菜品
	var foodCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("merchant_no = ? AND spec_id = ? AND state > ?", merchantNo, id, model.FoodSpecStateDeleted).
		Count(&foodCount).Error
	if err != nil {
		return err
	}
	if foodCount > 0 {
		return errors.BadRequest("", "FoodSpecHasRelatedFoods")
	}

	// 软删除规格
	return util.GetDB(ctx, serv.DB).
		Model(&model.FoodSpecModel{}).
		Where("id = ? AND merchant_no = ?", id, merchantNo).
		Updates(map[string]interface{}{
			"state":      model.FoodSpecStateDeleted,
			"deleted_at": time.Now(),
		}).Error
}

// GetSpecFoods 根据规格ID获取关联的美食列表
func (serv *FoodSpecService) GetSpecFoods(ctx context.Context, specID int64, merchantNo string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel

	db := util.GetDB(ctx, serv.DB).
		Where("spec_id = ? AND merchant_no = ? AND state > ?", specID, merchantNo, model.FoodSpecStateDeleted).
		Preload("FoodCategory", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name_zh, name_ug").Where("deleted_at IS NULL")
		}).
		Preload("ParentFood", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name_zh, name_ug").Where("deleted_at IS NULL")
		})

	err := db.Where("deleted_at IS NULL").Find(&foods).Error
	if err != nil {
		return nil, err
	}

	return foods, nil
}

// SaveFoodSpecFoods 保存规格美食关联关系
func (serv *FoodSpecService) SaveFoodSpecFoods(ctx context.Context, req *food_request.SaveFoodSpecFoodsRequest, merchantNo string) error {
	// 验证规格是否存在
	var spec model.FoodSpecModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND state > ?", req.SpecID, merchantNo, model.FoodSpecStateDeleted), &spec)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodSpecNotFound")
	}

	// 使用事务处理
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 提取所有需要验证的美食ID
		foodIDs := make([]int64, 0, len(req.Foods))
		for _, foodItem := range req.Foods {
			foodIDs = append(foodIDs, foodItem.Pid)
		}

		// 批量查询父级美食信息
		var parentFoods []model.FoodModel
		err := util.GetDB(ctx, serv.DB).
			Where("id IN ? AND merchant_no = ? AND state = 1 AND state > ?", foodIDs, merchantNo, model.FoodSpecStateDeleted).
			Find(&parentFoods).Error
		if err != nil {
			return err
		}

		// 验证所有美食都存在
		if len(parentFoods) != len(foodIDs) {
			return errors.NotFound("", "FoodNotFound")
		}

		// 创建父级美食ID到美食信息的映射
		parentFoodMap := make(map[int64]*model.FoodModel)
		for i := range parentFoods {
			parentFood := &parentFoods[i]
			// 验证美食类型和套餐状态
			if parentFood.IsCombo {
				return errors.BadRequest("", "FoodIsCombo")
			}
			if parentFood.SpecID != 0 {
				return errors.BadRequest("", "FoodIsSpec")
			}
			if parentFood.Type != model.FoodTypeFood {
				return errors.BadRequest("", "FoodTypeNotFood")
			}
			parentFoodMap[parentFood.ID] = parentFood
		}

		// 批量查询已存在的规格美食记录
		var existingSpecFoods []model.FoodModel
		err = util.GetDB(ctx, serv.DB).
			Where("pid IN ? AND spec_id = ? AND merchant_no = ? AND state > ?",
				foodIDs, req.SpecID, merchantNo, model.FoodSpecStateDeleted).
			Find(&existingSpecFoods).Error
		if err != nil {
			return err
		}

		// 创建已存在规格美食的映射
		existingSpecFoodMap := make(map[int64]*model.FoodModel)
		for i := range existingSpecFoods {
			existingSpecFood := &existingSpecFoods[i]
			existingSpecFoodMap[existingSpecFood.Pid] = existingSpecFood
		}

		// 准备批量更新和创建的数据
		var toUpdate []*model.FoodModel
		var toCreate []*model.FoodModel
		now := time.Now()

		for _, foodItem := range req.Foods {
			parentFood := parentFoodMap[foodItem.Pid]
			if existingSpecFood, exists := existingSpecFoodMap[foodItem.Pid]; exists {
				// 更新现有记录
				existingSpecFood.NameZh = spec.NameZh
				existingSpecFood.NameUg = spec.NameUg
				existingSpecFood.CostPrice = foodItem.CostPrice
				existingSpecFood.Price = foodItem.Price
				existingSpecFood.VipPrice = foodItem.VipPrice
				existingSpecFood.UpdatedAt = now
				toUpdate = append(toUpdate, existingSpecFood)
			} else {
				// 创建新记录
				specFood := &model.FoodModel{
					MerchantNo: merchantNo,
					Pid:        foodItem.Pid,
					SpecID:     req.SpecID,
					Type:       model.FoodTypeFood,
					NameZh:     spec.NameZh,
					NameUg:     spec.NameUg,
					CostPrice:  foodItem.CostPrice,
					Price:      foodItem.Price,
					VipPrice:   foodItem.VipPrice,
					// 继承原菜品的其他属性
					FoodCategoryID:   parentFood.FoodCategoryID,
					Image:            parentFood.Image,
					FormatID:         parentFood.FormatID,
					IsSpecialFood:    parentFood.IsSpecialFood,
					SupportScanOrder: parentFood.SupportScanOrder,
					IsCombo:          false,
					Sort:             parentFood.Sort,
					State:            parentFood.State,
					IsSync:           false,
					CreatedAt:        now,
					UpdatedAt:        now,
				}
				toCreate = append(toCreate, specFood)
			}
		}

		// 批量更新
		for _, specFood := range toUpdate {
			err = util.GetDB(ctx, serv.DB).Save(specFood).Error
			if err != nil {
				return err
			}
		}

		// 批量创建
		if len(toCreate) > 0 {
			err = util.GetDB(ctx, serv.DB).Create(&toCreate).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// RemoveFoodSpecFoods 解除规格美食关联关系
func (serv *FoodSpecService) RemoveFoodSpecFoods(ctx context.Context, req *food_request.RemoveFoodSpecFoodsRequest, merchantNo string) error {
	// 验证规格是否存在
	var spec model.FoodSpecModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND state > ?", req.SpecID, merchantNo, model.FoodSpecStateDeleted), &spec)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodSpecNotFound")
	}

	// 使用事务处理
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 批量验证要解除关联的美食是否都存在且属于该规格
		var existingSpecFoods []model.FoodModel
		err := util.GetDB(ctx, serv.DB).
			Where("pid IN ? AND spec_id = ? AND merchant_no = ? AND state > ?",
				req.FoodIDs, req.SpecID, merchantNo, model.FoodSpecStateDeleted).
			Find(&existingSpecFoods).Error
		if err != nil {
			return err
		}

		// 软删除规格美食关联记录
		now := time.Now()
		err = util.GetDB(ctx, serv.DB).
			Model(&model.FoodModel{}).
			Where("pid IN ? AND spec_id = ? AND merchant_no = ? AND state > ?",
				req.FoodIDs, req.SpecID, merchantNo, model.FoodSpecStateDeleted).
			Updates(map[string]interface{}{
				"deleted_at": &now,
				"updated_at": now,
			}).Error

		return err
	})
}

// SaveSort 保存规格排序
func (serv *FoodSpecService) SaveSort(ctx context.Context, req *food_request.SaveFoodSpecSortRequest, merchantNo string) error {
	// 开启事务
	return util.GetDB(ctx, serv.DB).Transaction(func(tx *gorm.DB) error {
		for _, item := range req.Items {
			// 验证规格是否属于当前商家
			var count int64
			err := tx.Model(&model.FoodSpecModel{}).
				Where("id = ? AND merchant_no = ? AND state > ?", item.ID, merchantNo, model.FoodSpecStateDeleted).
				Count(&count).Error
			if err != nil {
				return err
			}
			if count == 0 {
				return errors.BadRequest("", "FoodSpecNotFound")
			}

			// 更新排序
			err = tx.Model(&model.FoodSpecModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
			// 更新美食中的排序
			err = tx.Model(&model.FoodModel{}).
				Where("spec_id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
