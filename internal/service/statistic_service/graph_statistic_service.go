package statistic_service

import (
	"context"
	"fmt"
	"ros-api-go/internal/common/schema"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

type GraphStatisticService struct {
	DB *gorm.DB
}

func (serv *GraphStatisticService) GetStatistics(
	ctx context.Context, merchantNo string,
	beginAt, endAt time.Time) (int, []schema.GraphCustomerStatistics, []schema.GraphBusinessStatistics, []schema.GraphPayTypeStatistics) {
	// 计算天数差
	days := int(endAt.Sub(beginAt).Hours() / 24)
	var typeParam int

	typeParam = 1
	if days < 1 {
		typeParam = 3
	} else if days <= 31 {
		typeParam = 2
	}

	language := "zh"
	if !i18n.IsZh(&ctx) {
		language = "ug"
	}

	// 获取数据库连接
	db := util.GetDB(ctx, serv.DB)

	// 根据统计类型确定日期格式
	var dateFormat string
	switch typeParam {
	case 1:
		dateFormat = "%Y-%m"
	case 2:
		dateFormat = "%Y-%m-%d"
	default:
		dateFormat = "%Y-%m-%d %H:00"
	}

	// 使用Gorm实现get_customer_order_statistics_by_merchant_for_graph存储过程
	var customerStatistics []schema.GraphCustomerStatistics
	customerQuery := db.Table("orders").
		Select(
			"sum(customers_count) as day_customers_count, "+
				"count(id) as day_order_count, "+
				fmt.Sprintf("DATE_FORMAT(paid_at, '%s') as day_paid_at", dateFormat),
		).
		Where("merchant_no = ? AND state = 3 AND paid_at BETWEEN ? AND ?", merchantNo, beginAt, endAt).
		Group("day_paid_at").
		Order("day_paid_at asc")
	customerQuery.Scan(&customerStatistics)

	// 使用Gorm实现get_order_statistics_by_merchant_for_graph存储过程
	var orderStatistics []schema.GraphBusinessStatistics
	// 注意：这里使用的是updated_at，而不是paid_at，与原存储过程保持一致

	orderQuery := db.Table("orders").
		Select(
			fmt.Sprintf("DATE_FORMAT(updated_at, '%s') as day, ", dateFormat)+
				"SUM(price) as real_amount",
		).
		Where("state = 3 AND merchant_no = ? AND paid_at BETWEEN ? AND ?", merchantNo, beginAt, endAt).
		Group("day").
		Order("day ASC")
	orderQuery.Scan(&orderStatistics)

	// 使用Gorm实现get_payed_type_statistics_by_merchant_for_graph存储过程
	var payTypeStatistics []schema.GraphPayTypeStatistics

	// 构建字段选择语句，根据语言选择名称字段
	nameField := "payment_types.name_zh"
	if language == "ug" {
		nameField = "payment_types.name_ug"
	}

	// 构建子查询
	subQuery := db.Table("orders").
		Select("id, paid_at, no, price").
		Where("merchant_no = ? AND paid_at BETWEEN ? AND ? AND state = 3", merchantNo, beginAt, endAt)

	// 构建主查询
	payTypeQuery := db.Table("merchant_payments").
		Select(
			"sum(merchant_payments.amount / 100) as daily_price, "+
				fmt.Sprintf("DATE_FORMAT(orders.paid_at, '%s') as day, ", dateFormat)+
				fmt.Sprintf("%s as name, ", nameField)+
				"merchant_payments.payment_type_id as payment_type_id",
		).
		Joins("JOIN (?) as orders ON orders.no = merchant_payments.order_no", subQuery).
		Joins("LEFT JOIN payment_types ON merchant_payments.payment_type_id = payment_types.id").
		Group("day, payment_type_id").
		Order("day")
	payTypeQuery.Scan(&payTypeStatistics)

	// 确保返回的切片不为nil
	if customerStatistics == nil {
		customerStatistics = []schema.GraphCustomerStatistics{}
	}
	if orderStatistics == nil {
		orderStatistics = []schema.GraphBusinessStatistics{}
	}
	if payTypeStatistics == nil {
		payTypeStatistics = []schema.GraphPayTypeStatistics{}
	}

	return typeParam, customerStatistics, orderStatistics, payTypeStatistics
}
