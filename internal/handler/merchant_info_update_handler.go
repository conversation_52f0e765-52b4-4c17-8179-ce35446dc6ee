package handler

import (
	"context"
	"go.uber.org/zap"
	"log"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/logging"
)

type MerchantInfoUpdateHandler struct {
	MerchantInfoUpdateService *service.MerchantInfoUpdateService
}

type MerchantInfoUpdateType string

const (
	AreaUpdated         MerchantInfoUpdateType = "area_updated_at"
	TableUpdated        MerchantInfoUpdateType = "table_updated_at"
	FoodUpdated         MerchantInfoUpdateType = "food_updated_at"
	FoodRelationUpdated MerchantInfoUpdateType = "food_relation_updated_at"
	MethodUpdated       MerchantInfoUpdateType = "method_updated_at"
	RemarkUpdated       MerchantInfoUpdateType = "remark_updated_at"
	PaymentTypeUpdated  MerchantInfoUpdateType = "payment_type_updated_at"
	PrinterUpdated      MerchantInfoUpdateType = "printer_updated_at"
	FoodPrinterUpdated  MerchantInfoUpdateType = "food_printer_updated_at"
	PermissionUpdated   MerchantInfoUpdateType = "permission_updated_at"
	UserUpdated         MerchantInfoUpdateType = "user_updated_at"
)

func (handler *MerchantInfoUpdateHandler) UpdateMerchantInfoUpdates(merchantNo string, updateType MerchantInfoUpdateType) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logging.Context(context.Background()).Error("商家信息更新时间更新失败", zap.Any("err", err))
			}
		}()
		ctx := context.Background()
		// 使用GORM进行更新操作
		log.Println("column: ", updateType)

		if updateType != "" {
			err := handler.MerchantInfoUpdateService.UpdateColumn(ctx, merchantNo, string(updateType))
			if err != nil {
				logging.Context(ctx).Error("商家信息更新时间更新失败", zap.Error(err))
			}
		}
	}()
}
