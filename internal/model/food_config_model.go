package model

import "time"

const (
	GroupTypeMethod   = "method"
	GroupTypeAddition = "addition"
)

type FoodConfigModel struct {
	ID            int64     `gorm:"column:id;primary_key;autoIncrement"`          // ID
	MerchantNo    string    `gorm:"column:merchant_no;not null"`                  // 商户编号
	FoodID        int64     `gorm:"column:food_id;not null"`                      // 菜品ID
	GroupType     string    `gorm:"column:group_type;not null"`                   // 分组类型: method 做法分组 addition 加料分组
	GroupID       int64     `gorm:"column:group_id;not null"`                     // 分组ID
	Required      bool      `gorm:"column:required;default:0;not null"`           // 是否必选
	RequiredCount float64   `gorm:"column:required_count;default:1.000;not null"` // 必选数量
	UpperLimit    bool      `gorm:"column:upper_limit;default:0;not null"`        // 是否限制最多可选数量
	UpperCount    float64   `gorm:"column:upper_count;default:1.000;not null"`    // 最多可选数量
	RepeatChoice  bool      `gorm:"column:repeat_choice;default:0;not null"`      // 是否可重复选择
	CreatedAt     time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`  // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP"`  // 更新时间
}

func (FoodConfigModel) TableName() string {
	return "food_configs"
}
