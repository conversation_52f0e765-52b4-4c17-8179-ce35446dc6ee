package model

import (
	"time"
)

const (
	MethodStateDeleted = -1 // 删除
	MethodStateNormal  = 1  // 正常
)

// MethodModel 做法
type MethodModel struct {
	ID         int64      `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string     `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	GroupID    int64      `json:"group_id" gorm:"size:20;not null"`           // 做法分组ID
	NameZh     string     `json:"name_zh" gorm:"size:255;not null"`           // 做法名称(中文)
	NameUg     string     `json:"name_ug" gorm:"size:255;not null"`           // 做法名称(维语)
	DescZh     string     `json:"desc_zh" gorm:"size:255;not null"`           // 做法说明(中文)
	DescUg     string     `json:"desc_ug" gorm:"size:255;not null"`           // 做法说明(维语)
	Price      float64    `json:"price" gorm:"default:0"`                     // 价格
	Sort       int64      `json:"sort" gorm:"default:0"`                      // 排序
	State      int64      `json:"state" gorm:"default:1"`                     // 状态(-1 删除1:启用 0:停用)
	CreatedAt  time.Time  `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time  `gorm:"column:updated_at"`                          // 更新时间
	DeletedAt  *time.Time `gorm:"column:deleted_at"`                          // 删除时间

	// 关联字段
	Group      *MethodGroupModel `json:"group" gorm:"foreignKey:GroupID;references:ID"`  // 做法分组
	Foods      []*FoodModel      `json:"foods" gorm:"many2many:food_method;"`            // 关联的美食
	FoodsCount int64             `json:"foods_count" gorm:"<-:false;column:foods_count"` // 关联美食数量
}

func (m *MethodModel) TableName() string {
	return "methods"
}

// FoodMethodModel 美食-做法关联关系
type FoodMethodModel struct {
	ID         int64     `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string    `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	FoodID     int64     `json:"food_id" gorm:"size:20;not null"`            // 美食ID
	GroupID    int64     `json:"group_id" gorm:"size:20"`                    // 做法分组ID
	MethodID   int64     `json:"method_id" gorm:"size:20;not null"`          // 做法ID
	CreatedAt  time.Time `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`                          // 更新时间

	// 关联字段
	Food   *FoodModel        `json:"food" gorm:"foreignKey:FoodID;references:ID"`     // 美食
	Group  *MethodGroupModel `json:"group" gorm:"foreignKey:GroupID;references:ID"`   // 做法分组
	Method *MethodModel      `json:"method" gorm:"foreignKey:MethodID;references:ID"` // 做法
}

func (fm *FoodMethodModel) TableName() string {
	return "food_method"
}
