package model

import (
	"time"
)

// FoodAdditionModel 美食-加料关联关系
type FoodAdditionModel struct {
	ID         int64     `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string    `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	FoodID     int64     `json:"food_id" gorm:"size:20;not null"`            // 美食ID
	CategoryID int64     `json:"category_id" gorm:"size:20;not null"`        // 分组ID
	AdditionID int64     `json:"addition_id" gorm:"size:20;not null"`        // 加料ID
	CreatedAt  time.Time `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`                          // 更新时间

	// 关联字段
	Food     *FoodModel         `json:"food" gorm:"foreignKey:FoodID;references:ID"`         // 关联美食
	Addition *FoodModel         `json:"addition" gorm:"foreignKey:AdditionID;references:ID"` // 关联加料
	Category *FoodCategoryModel `json:"category" gorm:"foreignKey:CategoryID;references:ID"` // 关联分类
}

func (a *FoodAdditionModel) TableName() string {
	return "food_addition"
}
