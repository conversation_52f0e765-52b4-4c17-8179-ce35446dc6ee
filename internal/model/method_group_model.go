package model

import (
	"time"
)

const (
	MethodGroupStateDeleted = -1 // 删除
	MethodGroupStateNormal  = 1  // 正常
)

// MethodGroupModel 做法分组
type MethodGroupModel struct {
	ID         int64      `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string     `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	NameZh     string     `json:"name_zh" gorm:"size:32;not null"`            // 分组名称(中文)
	NameUg     string     `json:"name_ug" gorm:"size:32;not null"`            // 分组名称(维语)
	Sort       int64      `json:"sort" gorm:"default:0"`                      // 排序
	State      int64      `json:"state" gorm:"default:1"`                     // 状态(1:启用 -1:删除)
	CreatedAt  time.Time  `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time  `gorm:"column:updated_at"`                          // 更新时间
	DeletedAt  *time.Time `gorm:"column:deleted_at"`                          // 删除时间

	// 关联字段
	MethodsCount int64 `json:"methods_count" gorm:"<-:false;column:methods_count"` // 做法数量

	Methods []*MethodModel `json:"methods" gorm:"foreignKey:GroupID;references:ID"` // 做法
}

func (m *MethodGroupModel) TableName() string {
	return "method_groups"
}

// FoodMethodGroupModel 美食-做法关联关系
type FoodMethodGroupModel struct {
	ID         int64     `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string    `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	FoodID     int64     `json:"food_id" gorm:"size:20;not null"`            // 美食ID
	GroupID    int64     `json:"group_id" gorm:"size:20"`                    // 做法分组ID
	MethodID   int64     `json:"method_id" gorm:"size:20;not null"`          // 做法ID
	CreatedAt  time.Time `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`                          // 更新时间

	// 关联字段
	Food    *FoodModel        `json:"food" gorm:"foreignKey:FoodID;references:ID"`          // 美食
	Group   *MethodGroupModel `json:"group" gorm:"foreignKey:GroupID;references:ID"`        // 做法分组
	Methods *MethodModel      `json:"methods" gorm:"foreignKey:GroupID;references:GroupID"` // 做法
}

func (fm *FoodMethodGroupModel) TableName() string {
	return "food_method"
}
