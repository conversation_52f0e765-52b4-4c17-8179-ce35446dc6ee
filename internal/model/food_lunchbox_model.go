package model

import (
	"time"
)

// FoodLunchBoxModel 美食-餐盒关联关系
type FoodLunchBoxModel struct {
	ID         int64     `json:"id" gorm:"size:20;primaryKey;autoIncrement"` // ID
	MerchantNo string    `json:"merchant_no" gorm:"size:20;not null"`        // 商家编号
	FoodID     int64     `json:"food_id" gorm:"size:20;not null"`            // 美食ID
	LunchBoxID int64     `json:"lunch_box_id" gorm:"size:20;not null"`       // 餐盒ID
	Count      int64     `json:"count" gorm:"default:0"`                     // 餐盒数量
	CreatedAt  time.Time `gorm:"column:created_at"`                          // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`                          // 更新时间

	// 关联字段
	Food     *FoodModel `json:"food" gorm:"foreignKey:FoodID;references:ID"`          // 关联美食
	LunchBox *FoodModel `json:"lunch_box" gorm:"foreignKey:LunchBoxID;references:ID"` // 关联餐盒
}

func (a *FoodLunchBoxModel) TableName() string {
	return "food_lunch_box"
}
