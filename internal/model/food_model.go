package model

import (
	"time"
)

const (
	FoodStateDisabled = 0 // 删除
	FoodStateNormal   = 1 // 正常
)

const (
	FoodTypeFood     = 1 // 美食
	FoodTypeAddition = 2 // 加料
	FoodTypeLunchBox = 3 // 饭盒
)

// FoodModel 菜品
type FoodModel struct {
	ID               int64      `json:"id" gorm:"size:20;primaryKey;"`     // Unique ID
	MerchantNo       string     `json:"merchant_no" gorm:"size:20;"`       // 商家编号
	Pid              int64      `json:"pid" gorm:"size:20;"`               // 上级美食(规格)
	SpecID           int64      `json:"spec_id" gorm:"size:20;"`           // 规格ID
	Type             int64      `json:"type" gorm:"size:10;"`              // 类型 1: 美食, 2 加料, 3 饭盒
	FoodCategoryID   int64      `json:"food_category_id" gorm:"size:20;"`  // 菜品分类ID
	Image            string     `json:"image" gorm:"size:255;"`            // 图片
	ShortcutCode     string     `json:"shortcut_code" gorm:"size:20;"`     // 快捷码
	NameUg           string     `json:"name_ug" gorm:"size:50;"`           // 名称(维语)
	NameZh           string     `json:"name_zh" gorm:"size:50;"`           // 名称(中文)
	CostPrice        float64    `json:"cost_price"`                        // 定价
	VipPrice         float64    `json:"vip_price"`                         // 会员价格
	Price            float64    `json:"price"`                             // 现价
	FormatID         int        `json:"format_id" gorm:"size:20;"`         // 计费方式： 1-按份数收费，2-按重量收费
	IsSpecialFood    bool       `json:"is_special_food" gorm:"size:1;"`    // 是否特色菜
	SupportScanOrder bool       `json:"support_scan_order" gorm:"size:1;"` // 是否支持扫码点单
	CellClearState   bool       `json:"cell_clear_state" gorm:"size:1;"`   // 否设置剩余数量(1表示设置剩余数量、0表示没设置)
	SellClearCount   float64    `json:"sell_clear_count"`                  // 美食剩余限量数
	RemainingCount   float64    `json:"remaining_count"`                   // 美食剩余数量
	IsCombo          bool       `json:"is_combo_food" gorm:"size:1;"`      // 是否套餐菜
	Sort             int64      `json:"sort"`                              // 排序
	State            int64      `json:"state" gorm:"size:10;"`             // 状态
	IsSync           bool       `json:"is_sync" gorm:"size:1;"`            // 是否同步
	CreatedAt        time.Time  `gorm:"column:created_at"`                 // 创建时间
	UpdatedAt        time.Time  `gorm:"column:updated_at"`                 // 更新时间
	DeletedAt        *time.Time `gorm:"column:deleted_at"`                 // 删除时间

	SpecsCount int `gorm:"<-:false;column:specs_count"` // 规格数量

	Printers       []*PrinterModel      `json:"printers" gorm:"many2many:food_printers;foreignKey:ID;joinForeignKey:food_id;references:ID;joinReferences:printer_id"` // 打印机
	Combos         []*FoodComboModel    `gorm:"foreignKey:FoodID;references:ID"`                                                                                      // 套餐菜
	ParentFood     *FoodModel           `json:"parent_food" gorm:"foreignKey:Pid;references:ID"`                                                                      // 上级美食
	FoodCategory   *FoodCategoryModel   `json:"food_category" gorm:"foreignKey:FoodCategoryID;references:ID"`                                                         // 美食分类
	Spec           *FoodSpecModel       `json:"spec" gorm:"foreignKey:SpecID;references:ID"`                                                                          // 规格
	Specs          []*FoodModel         `json:"specs" gorm:"foreignKey:Pid;references:ID"`                                                                            // 菜品规格
	FoodLunchBoxes []*FoodLunchBoxModel `json:"lunch_boxes" gorm:"foreignKey:FoodID;references:ID"`                                                                   // 餐盒
	FoodMethods    []*FoodMethodModel   `json:"methods" gorm:"foreignKey:FoodID;references:ID"`                                                                       // 做法
	FoodAdditions  []*FoodAdditionModel `json:"additions" gorm:"foreignKey:FoodID;references:ID"`                                                                     // 做法
	FoodConfigs    []*FoodConfigModel   `json:"configs" gorm:"foreignKey:FoodID;references:ID"`                                                                       // 配置
}

func (a *FoodModel) TableName() string {
	return "foods"
}

type FoodBasicModel struct {
	ID             int64   `gorm:"column:id"`               // Unique ID
	MerchantNo     string  `gorm:"column:merchant_no"`      // 商家编号
	FoodCategoryID int64   `gorm:"column:food_category_id"` // 菜品分类ID
	NameUg         string  `gorm:"column:name_ug"`          // 名称(维语)
	NameZh         string  `gorm:"column:name_zh"`          // 名称(中文)
	CostPrice      float64 `gorm:"column:cost_price"`       // 定价
	VipPrice       float64 `gorm:"column:vip_price"`        // 会员价格
	Price          float64 `gorm:"column:price"`            // 现价
}

func (a *FoodBasicModel) TableName() string {
	return "foods"
}
