package order_request

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

// OrderSyncRequest 订单同步请求
type OrderSyncRequest struct {
	Order         *SyncOrderForm          `json:"order"  binding:"required,dive"`
	OrderDetails  []*SyncOrderDetailForm  `json:"details" binding:"dive"`
	OrderPayments []*SyncOrderPaymentForm `json:"payments"  binding:"required,dive"`
}

// Validate 验证请求
func (req *OrderSyncRequest) Validate(ctx context.Context) error {
	// 订单部分验证
	err := req.Order.Validate(ctx)
	if err != nil {
		return err
	}
	// 订单详情部分验证
	for _, detail := range req.OrderDetails {
		err = detail.Validate(ctx, req.Order)
		if err != nil {
			return err
		}
	}
	// 订单支付部分验证
	for _, payment := range req.OrderPayments {
		err = payment.Validate(ctx, req.Order)
		if err != nil {
			return err
		}
	}
	return nil
}

// SyncOrderForm 订单同步请求（订单部分）
type SyncOrderForm struct {
	No              string     `json:"no" binding:"required"`                                                 // 订单号
	SerialNumber    int        `json:"serial_number" binding:""`                                              // 订单序号
	PlateNumber     int        `json:"plate_number" binding:""`                                               // 牌号
	MerchantNo      string     `json:"-"`                                                                     // 商户号(系统填充)
	CustomerID      int64      `json:"customer_id" binding:""`                                                // VIP会员ID
	TableID         int64      `json:"table_id" binding:""`                                                   // 餐桌ID
	TerminalID      int64      `json:"terminal_id" binding:"required"`                                        // 终端ID
	CustomersCount  int        `json:"customers_count" binding:"required"`                                    // 顾客数
	FoodsCount      float64    `json:"foods_count" binding:""`                                                // 菜品数
	CostPrice       float64    `json:"cost_price" binding:"min=0"`                                            // 成本价
	OriginalPrice   float64    `json:"original_price" binding:"min=0"`                                        // 原价
	IgnorePrice     float64    `json:"ignore_price" binding:"min=0"`                                          // 原价
	VipPrice        float64    `json:"vip_price" binding:"min=0"`                                             // 会员价
	Price           float64    `json:"price" binding:"min=0"`                                                 // 实际价格
	CollectedAmount *float64   `json:"collected_amount" binding:""`                                           // 已收金额
	GiveChange      *float64   `json:"give_change" binding:""`                                                // 找零
	IsScanOrder     bool       `json:"is_scan_order" binding:""`                                              // 是否扫码点单
	WechatUserID    int64      `json:"wechat_user_id" binding:""`                                             // 微信用户ID
	UserID          int64      `json:"user_id" binding:""`                                                    // 用户ID
	CashierID       *int64     `json:"cashier_id" binding:""`                                                 // 收银员ID
	State           int64      `json:"state" binding:"required"`                                              // 订单状态
	Type            int        `json:"type" binding:""`                                                       // 订单类型
	Remarks         *string    `json:"remarks" binding:""`                                                    // 备注
	TaxTicket       bool       `json:"tex_ticket" binding:"boolean"`                                          // 是否开票
	IsPrint         bool       `json:"is_print" binding:"boolean"`                                            // 是否打印
	PaidAt          *time.Time `json:"paid_at" time_format:"2006-01-02T15:04:05Z07:00"`                       // 创建时间
	CreatedAt       *time.Time `json:"created_at" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"` // 创建时间
	UpdatedAt       *time.Time `json:"updated_at" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"` // 更新时间
}

// Validate 验证请求
func (req *SyncOrderForm) Validate(ctx context.Context) error {
	if req.IsScanOrder {
		if req.WechatUserID == 0 {
			return errors.BadRequest("", "微信用户ID不能为空")
		}
	}
	return nil
}

// FillTo 填充到模型
func (req *SyncOrderForm) FillTo(ctx context.Context, model *model.OrderModel) error {
	model.No = req.No
	model.SerialNumber = req.SerialNumber
	model.PlateNumber = req.PlateNumber
	model.Type = req.Type
	model.MerchantNo = req.MerchantNo
	model.CustomerID = req.CustomerID
	model.TableID = req.TableID
	model.TerminalID = req.TerminalID
	model.CustomersCount = req.CustomersCount
	model.FoodsCount = req.FoodsCount
	model.CostPrice = req.CostPrice
	model.OriginalPrice = req.OriginalPrice
	model.IgnorePrice = req.IgnorePrice
	model.VipPrice = req.VipPrice
	model.Price = req.Price

	if req.CollectedAmount != nil {
		model.CollectedAmount = req.CollectedAmount
	}
	if req.GiveChange != nil {
		model.GiveChange = req.GiveChange
	}
	model.UserID = req.UserID
	model.CashierID = req.CashierID
	model.State = req.State
	if req.Remarks != nil {
		model.Remarks = req.Remarks
	}
	model.TaxTicket = req.TaxTicket
	model.IsPrint = req.IsPrint

	model.PaidAt = req.PaidAt
	model.CreatedAt = req.CreatedAt
	model.UpdatedAt = req.UpdatedAt

	// 扫码点菜
	model.IsScanOrder = req.IsScanOrder
	model.WechatUserID = &req.WechatUserID

	return nil
}

type ComboInfoForm struct {
	ID     int64   `json:"id" binding:"required"`      // 套餐detail编号
	FoodID int64   `json:"food_id" binding:"required"` // 美食编号
	Count  float64 `json:"count" binding:"required"`   // 美食数量
}

// SyncOrderDetailForm 订单详情同步请求
type SyncOrderDetailForm struct {
	MerchantNo    string                        `json:"-" binding:""`                                                          // 商户号(系统填充)
	OrderNo       string                        `json:"-" binding:""`                                                          // 订单编号
	OrderID       int64                         `json:"-" binding:""`                                                          // 订单ID
	FoodID        int64                         `json:"food_id" binding:"required"`                                            // 菜品ID
	FoodsCount    float64                       `json:"foods_count" binding:""`                                                // 菜品数量
	CostPrice     float64                       `json:"cost_price" binding:"min=0"`                                            // 成本价
	OriginalPrice float64                       `json:"original_price" binding:"min=0"`                                        // 原价
	VipPrice      float64                       `json:"vip_price" binding:"min=0"`                                             // 会员价
	Price         float64                       `json:"price" binding:"min=0"`                                                 // 实际价格
	TotalPrice    float64                       `json:"total_price" binding:""`                                                // 总价
	IsPrint       bool                          `json:"is_print" binding:"boolean"`                                            // 是否打印
	UserID        int64                         `json:"user_id" binding:""`                                                    // 用户ID
	State         int                           `json:"state" binding:""`                                                      // 订单状态
	Remarks       *string                       `json:"remarks" binding:""`                                                    // 备注
	IsCombo       bool                          `json:"is_combo" binding:"boolean"`                                            // 是否套餐
	ComboInfo     *util.TArray[model.ComboInfo] `json:"combo_info" binding:""`                                                 // 套餐信息
	CreatedAt     *time.Time                    `json:"created_at" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"` // 创建时间
	UpdatedAt     *time.Time                    `json:"updated_at" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"` // 更新时间
}

// Validate 验证请求
func (req *SyncOrderDetailForm) Validate(ctx context.Context, order *SyncOrderForm) error {

	return nil
}

// FillTo 填充到模型
func (req *SyncOrderDetailForm) FillTo(ctx context.Context, model *model.OrderDetailModel) error {
	model.MerchantNo = req.MerchantNo
	model.OrderID = req.OrderID
	model.FoodID = req.FoodID
	model.FoodsCount = req.FoodsCount
	model.CostPrice = req.CostPrice
	model.OriginalPrice = req.OriginalPrice
	model.VipPrice = req.VipPrice
	model.Price = req.Price
	model.TotalPrice = req.TotalPrice
	model.IsPrint = req.IsPrint
	model.UserID = req.UserID
	model.State = req.State
	model.Remarks = req.Remarks
	model.IsCombo = req.IsCombo
	model.ComboInfo = req.ComboInfo
	model.CreatedAt = req.CreatedAt
	model.UpdatedAt = req.UpdatedAt

	return nil
}

// SyncOrderPaymentForm 商户支付记录同步请求
type SyncOrderPaymentForm struct {
	MerchantNo    string     `json:"-"  binding:""`                                                         // 商户编号
	OrderID       int64      `json:"-"  binding:""`                                                         // 订单ID (同步时填充)
	OrderNo       string     `json:"order_no"  binding:"required"`                                          // 订单编号
	PaymentTypeID int64      `json:"payment_type_id"  binding:"required"`                                   // 平台收款方式
	PaymentNo     string     `json:"payment_no"  binding:"required"`                                        // 支付编号
	Amount        float64    `json:"amount"  binding:"min=0"`                                               // 支付金额
	PayType       string     `json:"pay_type"  binding:""`                                                  // 支付方式： wechat/alipay/cash
	Remark        *string    `json:"remark"  binding:""`                                                    // 备注信息
	State         int64      `json:"state"  binding:""`                                                     // 支付状态： 0 未支付 1 已支付 2 已退款
	CashierID     int64      `json:"user_id"  binding:""`                                                   // 收银员ID
	PaidAt        *time.Time `json:"paid_at" binding:"" time_format:"2006-01-02T15:04:05Z07:00"`            // 支付时间
	CreatedAt     time.Time  `json:"created_at" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"` // 创建时间
	UpdatedAt     time.Time  `json:"updated_at" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"` // 更新时间
}

// Validate 验证请求
func (req *SyncOrderPaymentForm) Validate(ctx context.Context, order *SyncOrderForm) error {

	return nil
}

// FillTo 填充到模型
func (req *SyncOrderPaymentForm) FillTo(ctx context.Context, model *model.MerchantPaymentModel) error {
	model.MerchantNo = req.MerchantNo
	model.OrderID = req.OrderID
	model.OrderNo = req.OrderNo
	model.PaymentTypeID = req.PaymentTypeID
	model.PaymentNo = req.PaymentNo
	model.Amount = int64(util.MultiplyFloat(req.Amount, 100))
	model.PayType = req.PayType
	model.Remark = req.Remark
	model.Status = req.State
	model.PaidAt = req.PaidAt
	model.CashierID = req.CashierID
	model.CreatedAt = req.CreatedAt
	model.UpdatedAt = req.UpdatedAt

	switch req.PaymentTypeID {
	case 1:
		model.PayType = "wechat"
	case 10:
		model.PayType = "alipay"
	case 3:
		model.PayType = "cash"
	}
	return nil
}
