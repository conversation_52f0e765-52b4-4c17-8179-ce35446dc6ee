package food_request

// MethodListRequest 获取做法列表请求
type MethodListRequest struct {
	GroupID int64  `form:"group_id"` // 做法分组ID
	Keyword string `form:"keyword"`  // 搜索关键词
}

// CreateMethodRequest 创建做法请求
type CreateMethodRequest struct {
	GroupID int64   `json:"group_id" binding:"required,min=1"`  // 做法分组ID
	NameZh  string  `json:"name_zh" binding:"required,max=255"` // 做法名称(中文)
	NameUg  string  `json:"name_ug" binding:"required,max=255"` // 做法名称(维语)
	DescZh  string  `json:"desc_zh" binding:"max=255"`          // 做法说明(中文)
	DescUg  string  `json:"desc_ug" binding:"max=255"`          // 做法说明(维语)
	Price   float64 `json:"price" binding:"min=0"`              // 价格
}

// UpdateMethodRequest 更新做法请求
type UpdateMethodRequest struct {
	GroupID int64   `json:"group_id" binding:"required,min=1"`  // 做法分组ID
	NameZh  string  `json:"name_zh" binding:"required,max=255"` // 做法名称(中文)
	NameUg  string  `json:"name_ug" binding:"required,max=255"` // 做法名称(维语)
	DescZh  string  `json:"desc_zh" binding:"max=255"`          // 做法说明(中文)
	DescUg  string  `json:"desc_ug" binding:"max=255"`          // 做法说明(维语)
	Price   float64 `json:"price" binding:"min=0"`              // 价格
}

// MethodSortItem 做法排序项
type MethodSortItem struct {
	ID   int64 `json:"id" binding:"required"`   // 做法ID
	Sort int64 `json:"sort" binding:"required"` // 排序值
}

// SaveMethodSortRequest 保存做法排序请求
type SaveMethodSortRequest struct {
	Items []MethodSortItem `json:"items" binding:"required,dive"` // 排序项列表
}

// MethodFoodListRequest 获取做法关联美食列表请求
type MethodFoodListRequest struct {
	Keyword string `form:"keyword"` // 搜索关键词
}

// FoodMethodItem 美食做法关联项
type FoodMethodItem struct {
	FoodID int64   `json:"food_id" binding:"required,min=1"` // 美食ID
	Price  float64 `json:"price" binding:"min=0"`            // 做法价格，0表示使用默认价格
}

// SaveFoodMethodRequest 保存美食做法关联请求
type SaveFoodMethodRequest struct {
	MethodID int64            `json:"method_id" binding:"required,min=1"` // 做法ID
	Foods    []FoodMethodItem `json:"foods" binding:"dive"`               // 美食列表，空数组表示清空关联
}
