package food_request

// CreateFoodAdditionGroupRequest 创建加料分组请求
type CreateFoodAdditionGroupRequest struct {
	NameUg string `json:"name_ug" binding:"required"` // 名称(维语)
	NameZh string `json:"name_zh" binding:"required"` // 名称(中文)
}

// UpdateFoodAdditionGroupRequest 更新加料分组请求
type UpdateFoodAdditionGroupRequest struct {
	NameUg string `json:"name_ug" binding:"required"` // 名称(维语)
	NameZh string `json:"name_zh" binding:"required"` // 名称(中文)
}

// FoodAdditionGroupListRequest 获取加料分组列表请求
type FoodAdditionGroupListRequest struct {
	MerchantNo string `form:"merchant_no"` // 商户编号
	State      *int64 `form:"state"`       // 状态过滤：0-禁用 1-启用
}

// FoodAdditionGroupSortItem 加料分组排序项
type FoodAdditionGroupSortItem struct {
	ID   int64 `json:"id" binding:"required"`   // 分类ID
	Sort int64 `json:"sort" binding:"required"` // 排序值
}

// SaveFoodAdditionGroupSortRequest 保存加料分组排序请求
type SaveFoodAdditionGroupSortRequest struct {
	Items []FoodAdditionGroupSortItem `json:"items" binding:"required,dive"` // 排序项列表
}
