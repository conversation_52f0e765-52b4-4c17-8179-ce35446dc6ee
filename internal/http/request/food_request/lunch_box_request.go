package food_request

// LunchBoxListRequest 餐盒列表查询请求
type LunchBoxListRequest struct {
	Keyword string `form:"keyword"` // 搜索关键词（支持中文和维语名称搜索）
	State   *int64 `form:"state"`   // 状态：0-禁用 1-启用
}

// CreateLunchBoxRequest 创建餐盒请求
type CreateLunchBoxRequest struct {
	Image            string  `json:"image"`                               // 图片
	ShortcutCode     string  `json:"shortcut_code"`                       // 快捷码
	NameUg           string  `json:"name_ug" binding:"required"`          // 名称(维语)
	NameZh           string  `json:"name_zh" binding:"required"`          // 名称(中文)
	CostPrice        float64 `json:"cost_price" binding:"required,min=0"` // 定价
	VipPrice         float64 `json:"vip_price" binding:"required,min=0"`  // 会员价
	Price            float64 `json:"price" binding:"required,min=0"`      // 现价
	FormatID         int     `json:"format_id" binding:"required,min=1"`  // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`                     // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"`                  // 是否支持扫码点单
	Sort             int64   `json:"sort"`                                // 排序
	State            int64   `json:"state" binding:"min=0,max=1"`         // 状态
}

// UpdateLunchBoxRequest 更新餐盒请求
type UpdateLunchBoxRequest struct {
	Image            string  `json:"image"`                               // 图片
	ShortcutCode     string  `json:"shortcut_code"`                       // 快捷码
	NameUg           string  `json:"name_ug" binding:"required"`          // 名称(维语)
	NameZh           string  `json:"name_zh" binding:"required"`          // 名称(中文)
	CostPrice        float64 `json:"cost_price" binding:"required,min=0"` // 定价
	VipPrice         float64 `json:"vip_price" binding:"required,min=0"`  // 会员价
	Price            float64 `json:"price" binding:"required,min=0"`      // 现价
	FormatID         int     `json:"format_id" binding:"required,min=1"`  // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`                     // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"`                  // 是否支持扫码点单
	Sort             int64   `json:"sort"`                                // 排序
	State            int64   `json:"state" binding:"min=0,max=1"`         // 状态
}

// SaveLunchBoxSortRequest 保存餐盒排序请求
type SaveLunchBoxSortRequest struct {
	LunchBoxes []LunchBoxSortItem `json:"lunch_boxes" binding:"required,dive"` // 餐盒排序列表
}

// LunchBoxSortItem 餐盒排序项
type LunchBoxSortItem struct {
	ID   int64 `json:"id" binding:"required,min=1"`   // 餐盒ID
	Sort int64 `json:"sort" binding:"required,min=0"` // 排序值
}

// SaveLunchBoxFoodsRequest 关联菜品请求
type SaveLunchBoxFoodsRequest struct {
	LunchBoxID int64              `json:"lunch_box_id" binding:"required,min=1"` // 餐盒ID
	Foods      []LunchBoxFoodItem `json:"foods" binding:"required,dive"`         // 关联的菜品列表
}

// LunchBoxFoodItem 关联菜品项
type LunchBoxFoodItem struct {
	FoodID int64 `json:"food_id" binding:"required,min=1"` // 菜品ID
	Count  int64 `json:"count" binding:"min=0"`            // 餐盒数量
}

// RemoveLunchBoxFoodsRequest 解除关联请求
type RemoveLunchBoxFoodsRequest struct {
	LunchBoxID int64   `json:"lunch_box_id" binding:"required,min=1"`  // 餐盒ID
	FoodIDs    []int64 `json:"food_ids" binding:"required,dive,min=1"` // 菜品ID列表
}

// LunchBoxFoodsRequest 获取餐盒关联的美食列表请求
type LunchBoxFoodsRequest struct {
}
