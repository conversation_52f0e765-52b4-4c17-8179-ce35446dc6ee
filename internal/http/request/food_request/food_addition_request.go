package food_request

// FoodAdditionListRequest 加料列表查询请求
type FoodAdditionListRequest struct {
	CategoryID int64  `form:"category_id"` // 加料分类ID
	Keyword    string `form:"keyword"`     // 搜索关键词（支持中文和维语名称搜索）
	State      *int64 `form:"state"`       // 状态：0-禁用 1-启用
}

// CreateFoodAdditionRequest 创建加料请求
type CreateFoodAdditionRequest struct {
	FoodCategoryID   int64   `json:"food_category_id" binding:"required,min=1"` // 加料分类ID
	Image            string  `json:"image"`                                     // 图片
	ShortcutCode     string  `json:"shortcut_code"`                             // 快捷码
	NameUg           string  `json:"name_ug" binding:"required"`                // 名称(维语)
	NameZh           string  `json:"name_zh" binding:"required"`                // 名称(中文)
	CostPrice        float64 `json:"cost_price" binding:"required,min=0"`       // 定价
	VipPrice         float64 `json:"vip_price" binding:"required,min=0"`        // 会员价
	Price            float64 `json:"price" binding:"required,min=0"`            // 现价
	FormatID         int     `json:"format_id" binding:"required,min=1"`        // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`                           // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"`                        // 是否支持扫码点单
	Sort             int64   `json:"sort"`                                      // 排序
	State            int64   `json:"state" binding:"min=0,max=1"`               // 状态
}

// UpdateFoodAdditionRequest 更新加料请求
type UpdateFoodAdditionRequest struct {
	FoodCategoryID   int64   `json:"food_category_id" binding:"required,min=1"` // 加料分类ID
	Image            string  `json:"image"`                                     // 图片
	ShortcutCode     string  `json:"shortcut_code"`                             // 快捷码
	NameUg           string  `json:"name_ug" binding:"required"`                // 名称(维语)
	NameZh           string  `json:"name_zh" binding:"required"`                // 名称(中文)
	CostPrice        float64 `json:"cost_price" binding:"required,min=0"`       // 定价
	VipPrice         float64 `json:"vip_price" binding:"required,min=0"`        // 会员价
	Price            float64 `json:"price" binding:"required,min=0"`            // 现价
	FormatID         int     `json:"format_id" binding:"required,min=1"`        // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`                           // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"`                        // 是否支持扫码点单
	Sort             int64   `json:"sort"`                                      // 排序
	State            int64   `json:"state" binding:"min=0,max=1"`               // 状态
}

// SaveFoodAdditionFoodsRequest 关联菜品请求
type SaveFoodAdditionFoodsRequest struct {
	AdditionID int64                       `json:"addition_id" binding:"required,min=1"` // 加料ID
	Foods      []FoodAdditionAssociateItem `json:"foods" binding:"required,dive"`        // 关联的菜品列表
}

// FoodAdditionAssociateItem 关联菜品项
type FoodAdditionAssociateItem struct {
	FoodID int64   `json:"food_id" binding:"required,min=1"` // 菜品ID
	Price  float64 `json:"price" binding:"min=0"`            // 加料价格
}

// RemoveFoodAdditionFoodsRequest 解除关联请求
type RemoveFoodAdditionFoodsRequest struct {
	AdditionID int64   `json:"addition_id" binding:"required,min=1"`   // 加料ID
	FoodIDs    []int64 `json:"food_ids" binding:"required,dive,min=1"` // 菜品ID列表
}

// GetFoodAdditionFoodsRequest 获取加料关联的美食列表请求
type GetFoodAdditionFoodsRequest struct {
}
