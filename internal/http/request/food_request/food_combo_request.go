package food_request

// ComboFoodRequest 套餐菜品请求参数
type ComboFoodRequest struct {
	ComboFoodIds []int64 `json:"combo_food_ids" binding:"required"`    // 套餐菜品ID（逗号分隔）
	ComboPrice   float64 `json:"combo_price" binding:"required,min=0"` // 套餐价格
	ComboCount   int     `json:"combo_count" binding:"required,min=1"` // 套餐数量
	NameUg       string  `json:"name_ug" binding:"required"`           // 名称(维语)
	NameZh       string  `json:"name_zh" binding:"required"`           // 名称(中文)
}

// FoodComboRequest 创建套餐请求参数
type FoodComboRequest struct {
	FoodCategoryID   int64              `json:"food_category_id" binding:"required,min=1"` // 分类ID
	Image            string             `json:"image"`                                     // 图片
	ShortcutCode     string             `json:"shortcut_code" binding:"required,max=4"`    // 快捷码
	NameUg           string             `json:"name_ug" binding:"required,max=100"`        // 名称(维语)
	NameZh           string             `json:"name_zh" binding:"required,max=100"`        // 名称(中文)
	CostPrice        float64            `json:"cost_price" binding:"required,min=0"`       // 定价
	VipPrice         float64            `json:"vip_price" binding:"required,min=0"`        // 会员价
	Price            float64            `json:"price" binding:"required,min=0"`            // 现价
	IsSpecialFood    bool               `json:"is_special_food"`                           // 是否特色菜
	SupportScanOrder bool               `json:"support_scan_order"`                        // 是否支持扫码点单
	IsCombo          bool               ``                                                 // 是否套餐菜(取true)
	Sort             int64              `json:"sort"`                                      // 排序
	State            int64              `json:"state" binding:"min=0,max=1"`               // 状态
	ComboFoods       []ComboFoodRequest `json:"combo_foods" binding:"required,dive"`       // 套餐菜品列表
}
