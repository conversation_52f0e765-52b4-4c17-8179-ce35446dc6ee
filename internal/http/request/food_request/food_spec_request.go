package food_request

// CreateFoodSpecRequest 创建规格请求
type CreateFoodSpecRequest struct {
	NameZh string `json:"name_zh" binding:"required"` // 规格名称(中文)
	NameUg string `json:"name_ug" binding:"required"` // 规格名称(维语)
}

// UpdateFoodSpecRequest 更新规格请求
type UpdateFoodSpecRequest struct {
	NameZh string `json:"name_zh" binding:"required"` // 规格名称(中文)
	NameUg string `json:"name_ug" binding:"required"` // 规格名称(维语)
}

// FoodSpecListRequest 规格列表查询请求
type FoodSpecListRequest struct {
	Keyword string `form:"keyword"` // 搜索关键词（支持中文和维语名称搜索）
}

// FoodSpecSortItem 规格排序项
type FoodSpecSortItem struct {
	ID   int64 `json:"id" binding:"required"`   // 规格ID
	Sort int64 `json:"sort" binding:"required"` // 排序值
}

// SaveFoodSpecSortRequest 保存规格排序请求
type SaveFoodSpecSortRequest struct {
	Items []FoodSpecSortItem `json:"items" binding:"required,dive"` // 排序项列表
}

// FoodSpecFoodItem 规格美食项
type FoodSpecFoodItem struct {
	Pid       int64   `json:"pid" binding:"required"`       // 美食ID
	CostPrice float64 `json:"cost_price" binding:"number"`  // 价格
	Price     float64 `json:"price" binding:"required"`     // 价格
	VipPrice  float64 `json:"vip_price" binding:"required"` // 会员价格
}

// SaveFoodSpecFoodsRequest 保存规格美食关联关系请求
type SaveFoodSpecFoodsRequest struct {
	SpecID int64              `json:"spec_id" binding:"required"` // 规格ID
	Foods  []FoodSpecFoodItem `json:"foods" binding:"required"`   // 美食列表
}

// RemoveFoodSpecFoodsRequest 解除规格美食关联关系请求
type RemoveFoodSpecFoodsRequest struct {
	SpecID  int64   `json:"spec_id" binding:"required"`  // 规格ID
	FoodIDs []int64 `json:"food_ids" binding:"required"` // 要解除关联的美食ID列表
}
