package food_request

type FoodSellClearDataRequest struct {
	MerchantNo     string  `json:"-"`                                       // 商户号
	ID             int64   `json:"id" binding:"required,min=1"`             // 菜品ID
	CellClearState bool    `json:"cell_clear_state" binding:"boolean"`      // 沽清状态：1-有库存，0-无库存
	SellClearCount float64 `json:"sell_clear_count" binding:"number,min=0"` // 售清数量
	RemainingCount float64 `json:"remaining_count" binding:"number,min=0"`  // 剩余库存数量
}

// FoodListRequest 美食列表请求参数
type FoodListRequest struct {
	MerchantNo       string `form:"-"`                  // 商户号
	FoodCategoryID   int64  `form:"food_category_id"`   // 分类ID
	Keyword          string `form:"keyword"`            // 关键词
	State            *int   `form:"state"`              // 状态
	SellClearAll     string `form:"sell_clear_all"`     // 是否清理所有销售
	SupportScanOrder *bool  `form:"support_scan_order"` // 是否支持扫码点单
}

// FoodSpecRequest 规格菜品请求参数
type FoodSpecRequest struct {
	SpecId     int64                 `json:"spec_id" binding:"required"`          // 规格ID
	Price      float64               `json:"price" binding:"required,min=0"`      // 价格
	VipPrice   float64               `json:"vip_price" binding:"required,min=0"`  // 会员价格
	CostPrice  float64               `json:"cost_price" binding:"required,min=0"` // 成本价
	LunchBoxes []FoodLunchBoxRequest `json:"lunch_boxes" binding:"dive"`          // 餐盒列表
}

type FoodLunchBoxRequest struct {
	LunchBoxID int64 `json:"lunch_box_id" binding:"required,min=1"` // 美食ID
	Count      int64 `json:"count" binding:"required,min=1"`        // 数量
}

type FoodConfigRequest struct {
	GroupID       int64   `from:"group_id" binding:"required,min=1"` // 分组ID
	Required      bool    `from:"required"`                          // 是否必选
	RequiredCount float64 `from:"required_count" binding:"min=0"`    // 必选数量
	UpperLimit    bool    `from:"upper_limit"`                       // 是否限制最多可选数量
	UpperCount    float64 `from:"upper_count" binding:"min=0"`       // 最多可选数量
	RepeatChoice  bool    `from:"repeat_choice"`                     // 是否可重复选择
}

// FoodRequest 创建美食请求参数
type FoodRequest struct {
	FoodCategoryID   int64               `json:"food_category_id" binding:"required,min=1"` // 分类ID
	Image            string              `json:"image"`                                     // 图片
	ShortcutCode     string              `json:"shortcut_code"`                             // 快捷码
	NameUg           string              `json:"name_ug" binding:"required"`                // 名称(维语)
	NameZh           string              `json:"name_zh" binding:"required"`                // 名称(中文)
	CostPrice        float64             `json:"cost_price" binding:"min=0"`                // 定价
	VipPrice         float64             `json:"vip_price" binding:"min=0"`                 // 会员价
	Price            float64             `json:"price" binding:"min=0"`                     // 现价
	FormatID         int                 `json:"format_id" binding:"required,min=1"`        // 规格ID
	IsSpecialFood    bool                `json:"is_special_food"`                           // 是否特色菜
	SupportScanOrder bool                `json:"support_scan_order"`                        // 是否支持扫码点单
	CellClearState   bool                `json:"cell_clear_state"`                          // 是否设置剩余数量
	SellClearCount   float64             `json:"sell_clear_count" binding:"min=0"`          // 美食剩余限量数
	RemainingCount   float64             `json:"remaining_count" binding:"min=0"`           // 美食剩余数量
	IsCombo          bool                `json:"is_combo"`                                  // 是否套餐菜
	Sort             int64               `json:"sort"`                                      // 排序
	State            int64               `json:"state" binding:"min=0,max=1"`               // 状态
	Specs            []FoodSpecRequest   `json:"specs" binding:"required,dive"`             // 套餐菜品列表
	MethodIds        []int64             `json:"method_ids"`                                // 做法ID列表
	AdditionIds      []int64             `json:"addition_ids"`                              // 加料ID列表
	MethodConfigs    []FoodConfigRequest `json:"method_configs"`                            // 做法配置
	AdditionConfigs  []FoodConfigRequest `json:"addition_configs"`                          // 加料配置
}

// FoodSortItem 美食排序项
type FoodSortItem struct {
	ID   int64 `json:"id" binding:"required"`   // 美食ID
	Sort int64 `json:"sort" binding:"required"` // 排序值
}

// SaveFoodSortRequest 保存美食排序请求
type SaveFoodSortRequest struct {
	Items []FoodSortItem `json:"items" binding:"required,dive"` // 排序项列表
}
