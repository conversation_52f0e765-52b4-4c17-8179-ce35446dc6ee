package order_resource

import (
	"context"
	"github.com/samber/lo"
	"ros-api-go/internal/config"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/model"
)

type FoodResourceV3 struct {
	ID               int64                                       `json:"id"`
	MerchantNo       string                                      `json:"merchant_no"`
	FoodCategoryID   int64                                       `json:"food_category_id"`
	Image            string                                      `json:"image"`
	ShortcutCode     string                                      `json:"shortcut_code"`
	NameUg           string                                      `json:"name_ug"`
	NameZh           string                                      `json:"name_zh"`
	CostPrice        float64                                     `json:"cost_price"`
	VipPrice         float64                                     `json:"vip_price"`
	Price            float64                                     `json:"price"`
	FoodFormatID     int                                         `json:"food_format_id"`
	SpecID           int64                                       `json:"spec_id"`
	IsSpecialFood    bool                                        `json:"is_special_food"`
	SupportScanOrder bool                                        `json:"support_scan_order"`
	CellClearState   bool                                        `json:"cell_clear_state"`
	SellClearCount   float64                                     `json:"sell_clear_count"`
	RemainingCount   float64                                     `json:"remaining_count"`
	IsComboFood      bool                                        `json:"is_combo_food"`
	Sort             int64                                       `json:"sort"`
	State            int64                                       `json:"state"`
	Spec             *food_resource.FoodSpecResource             `json:"spec"`
	Combos           []*Combo                                    `json:"combos"`
	Specs            []*FoodResourceV3                           `json:"specs,omitempty"`
	MethodGroups     []*food_resource.MethodGroupResource        `json:"method_groups"`
	LunchBoxes       []*FoodResourceV3                           `json:"lunch_boxes"`
	AdditionsGroups  []*food_resource.FoodCategorySimpleResource `json:"additions_groups"`
	MethodConfigs    []*food_resource.FoodConfigResource         `json:"method_configs"`
	AdditionConfigs  []*food_resource.FoodConfigResource         `json:"addition_configs"`
}

func (rc *FoodResourceV3) Make(ctx *context.Context, item *model.FoodModel) *FoodResourceV3 {
	if item == nil {
		return nil
	}

	data := FoodResourceV3{
		ID:               item.ID,
		MerchantNo:       item.MerchantNo,
		FoodCategoryID:   item.FoodCategoryID,
		ShortcutCode:     item.ShortcutCode,
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FoodFormatID:     item.FormatID,
		SpecID:           item.SpecID,
		IsSpecialFood:    item.IsSpecialFood,
		SupportScanOrder: item.SupportScanOrder,
		CellClearState:   item.CellClearState,
		SellClearCount:   item.SellClearCount,
		RemainingCount:   item.RemainingCount,
		IsComboFood:      item.IsCombo,
		Sort:             item.Sort,
		State:            item.State,
	}

	// 如果不是规格, 则获取图片
	if item.SpecID == 0 {
		data.Image = config.C.Storage.OSS.Endpoint + item.Image
	}

	// 处理规格
	if item.Spec != nil {
		data.Spec = (&food_resource.FoodSpecResource{}).Make(item.Spec)
	}

	// 处理套餐
	data.Combos = (&Combo{}).Collection(item.Combos)

	// 处理配置
	data.MethodConfigs = []*food_resource.FoodConfigResource{}
	data.AdditionConfigs = []*food_resource.FoodConfigResource{}
	if item.FoodConfigs != nil && len(item.FoodConfigs) > 0 {
		for _, foodConfig := range item.FoodConfigs {
			if foodConfig.GroupType == model.GroupTypeMethod {
				data.MethodConfigs = append(data.MethodConfigs, (&food_resource.FoodConfigResource{}).Make(foodConfig))
			} else {
				data.AdditionConfigs = append(data.AdditionConfigs, (&food_resource.FoodConfigResource{}).Make(foodConfig))
			}
		}
	}

	// 处理加料
	data.AdditionsGroups = []*food_resource.FoodCategorySimpleResource{}
	if item.FoodAdditions != nil && len(item.FoodAdditions) > 0 {
		groups := lo.GroupBy(item.FoodAdditions, func(addition *model.FoodAdditionModel) int64 {
			return addition.CategoryID
		})

		for _, group := range groups {
			foods := make([]*food_resource.FoodResource, len(group))
			for i, addition := range group {
				foods[i] = (&food_resource.FoodResource{}).Make(ctx, addition.Addition)
			}
			data.AdditionsGroups = append(data.AdditionsGroups, &food_resource.FoodCategorySimpleResource{
				ID:     group[0].CategoryID,
				NameZh: group[0].Category.NameZh,
				NameUg: group[0].Category.NameUg,
				Foods:  foods,
			})
		}
	}

	// 处理做法
	data.MethodGroups = []*food_resource.MethodGroupResource{}
	if item.FoodMethods != nil && len(item.FoodMethods) > 0 {
		groups := lo.GroupBy(item.FoodMethods, func(method *model.FoodMethodModel) int64 {
			return method.GroupID
		})

		for _, group := range groups {
			methods := make([]*food_resource.MethodResource, len(group))
			for i, method := range group {
				methods[i] = (&food_resource.MethodResource{}).Make(method.Method)
			}
			data.MethodGroups = append(data.MethodGroups, &food_resource.MethodGroupResource{
				ID:      group[0].GroupID,
				NameZh:  group[0].Group.NameZh,
				NameUg:  group[0].Group.NameUg,
				Methods: methods,
			})
		}
	}

	// 处理餐盒
	data.LunchBoxes = []*FoodResourceV3{}
	if item.SpecID > 0 {
		if item.FoodLunchBoxes != nil && len(item.FoodLunchBoxes) > 0 {
			resource := &FoodResourceV3{}
			for _, lunchBox := range item.FoodLunchBoxes {
				data.LunchBoxes = append(data.LunchBoxes, resource.Make(ctx, lunchBox.LunchBox))
			}
		}
	}

	// 处理规格
	if item.Specs != nil && len(item.Specs) > 0 {
		data.Specs = (&FoodResourceV3{}).Collection(ctx, item.Specs)
	}

	return &data
}

func (rc *FoodResourceV3) Collection(ctx *context.Context, items []*model.FoodModel) []*FoodResourceV3 {

	if items == nil || len(items) == 0 {
		return []*FoodResourceV3{}
	}
	data := make([]*FoodResourceV3, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}
