package order_resource

import (
	"context"
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type FoodResource struct {
	ID               int64    `json:"id"`
	MerchantNo       string   `json:"merchant_no"`
	FoodCategoryID   int64    `json:"food_category_id"`
	Image            string   `json:"image"`
	ShortcutCode     string   `json:"shortcut_code"`
	NameUg           string   `json:"name_ug"`
	NameZh           string   `json:"name_zh"`
	CostPrice        float64  `json:"cost_price"`
	VipPrice         float64  `json:"vip_price"`
	Price            float64  `json:"price"`
	FormatID         int      `json:"format_id"`
	IsSpecialFood    bool     `json:"is_special_food"`
	SupportScanOrder bool     `json:"support_scan_order"`
	CellClearState   bool     `json:"cell_clear_state"`
	SellClearCount   float64  `json:"sell_clear_count"`
	RemainingCount   float64  `json:"remaining_count"`
	IsComboFood      bool     `json:"is_combo_food"`
	Sort             int64    `json:"sort"`
	State            int64    `json:"state"`
	IsSync           bool     `json:"is_sync"`
	CreatedAt        *string  `json:"created_at"`
	UpdatedAt        *string  `json:"updated_at"`
	Combos           []*Combo `json:"combos" gorm:"foreignkey:LunchBoxID;references:ID"`
}

type Combo struct {
	ID         int64    `json:"id"`
	ComboID    int64    `json:"combo_id"`
	FoodID     int64    `json:"food_id" `
	ParentID   int64    `json:"parent_id"`
	Type       int64    `json:"type"`
	ComboPrice float64  `json:"combo_price"`
	NameUg     string   `json:"name_ug"`
	NameZh     string   `json:"name_zh"`
	Count      int      `json:"count"`
	Childs     []*Combo `json:"childs" gorm:"foreignkey:ParentID;references:ID"`
}

func (combo *Combo) Make(item *model.FoodComboModel) *Combo {
	if item == nil {
		return nil
	}

	data := Combo{
		ID:         item.ID,
		ComboID:    item.ComboID,
		FoodID:     item.FoodID,
		ParentID:   item.ParentID,
		Type:       util.StrToInt64(item.Type),
		ComboPrice: item.ComboPrice,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		Count:      item.Count,
	}

	if item.Childs != nil {
		childs := make([]*Combo, len(item.Childs))
		for j, child := range item.Childs {
			childs[j] = combo.Make(&child)
		}
		data.Childs = childs
	}

	return &data
}

func (combo *Combo) Collection(items []*model.FoodComboModel) []*Combo {

	if items == nil || len(items) == 0 {
		return []*Combo{}
	}
	data := make([]*Combo, len(items))
	for i, food := range items {
		data[i] = combo.Make(food)
	}
	return data
}

func (rc *FoodResource) Make(ctx *context.Context, item *model.FoodModel, parentFood *model.FoodModel) *FoodResource {
	if item == nil {
		return nil
	}

	data := FoodResource{
		ID:               item.ID,
		MerchantNo:       item.MerchantNo,
		FoodCategoryID:   item.FoodCategoryID,
		Image:            config.C.Storage.OSS.Endpoint + item.Image,
		ShortcutCode:     item.ShortcutCode,
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FormatID:         item.FormatID,
		IsSpecialFood:    item.IsSpecialFood,
		SupportScanOrder: item.SupportScanOrder,
		CellClearState:   item.CellClearState,
		SellClearCount:   item.SellClearCount,
		RemainingCount:   item.RemainingCount,
		IsComboFood:      item.IsCombo,
		Sort:             item.Sort,
		State:            item.State,
		IsSync:           item.IsSync,
		CreatedAt:        util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:        util.FormatDateTime(&item.UpdatedAt),
	}

	if parentFood != nil {
		data.NameUg = parentFood.NameUg + "-" + item.NameUg
		data.NameZh = parentFood.NameZh + "-" + item.NameZh
	}

	data.Combos = (&Combo{}).Collection(item.Combos)

	return &data
}

func (rc *FoodResource) Collection(ctx *context.Context, items []*model.FoodModel) []*FoodResource {

	if items == nil || len(items) == 0 {
		return []*FoodResource{}
	}
	data := make([]*FoodResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item, nil)
	}
	return data
}
