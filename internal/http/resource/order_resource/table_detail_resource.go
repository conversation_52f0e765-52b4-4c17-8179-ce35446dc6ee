package order_resource

import (
	"context"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type TablesDetailResource struct {
	Table          *TableResource                        `json:"table"`
	FoodCategories []*food_resource.FoodCategoryResource `json:"food_categories"`
	Foods          []*food_resource.FoodResource         `json:"foods"`
	PayType        []*resource.PaymentTypeResource       `json:"pay_type"`
	DiancaiPay     int                                   `json:"diancai_pay"`
}

type TableResource struct {
	ID              int64            `json:"id" gorm:"primaryKey"`
	CustomersCount  int              `json:"customers_count"`
	AreaID          int64            `json:"area_id"`
	NameUg          string           `json:"name_ug"`
	NameZh          string           `json:"name_zh"`
	No              string           `json:"no"`
	SeatingCapacity int              `json:"seating_capacity"`
	Orders          []*OrderResource `json:"orders"`
}

func (rc *TableResource) Make(ctx context.Context, item *model.TableModel) *TableResource {
	if item == nil {
		return nil
	}
	data := TableResource{
		ID:              item.ID,
		CustomersCount:  0,
		AreaID:          item.AreaID,
		NameUg:          item.NameUg,
		NameZh:          item.NameZh,
		No:              item.No,
		SeatingCapacity: item.SeatingCapacity,
	}
	data.Orders = []*OrderResource{}
	orderResource := OrderResource{}
	if item.CloudOrders != nil {
		for _, order := range item.CloudOrders {
			data.CustomersCount += order.CustomersCount
			data.Orders = append(data.Orders, orderResource.Make(ctx, order))
		}
	}
	return &data
}

func (rc *TableResource) Collection(ctx context.Context, items []*model.TableModel) []*TableResource {

	if items == nil || len(items) == 0 {
		return []*TableResource{}
	}
	data := make([]*TableResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

type OrderResource struct {
	ID              int64                  `json:"id"`
	No              string                 `json:"order_no" gorm:"no"`
	TableID         int64                  `json:"table_id"`
	CustomersCount  int                    `json:"customers_count"`
	FoodsCount      float64                `json:"foods_count"`
	OriginalPrice   float64                `json:"original_price"`
	VipPrice        float64                `json:"vip_price"`
	Price           float64                `json:"price"`
	IgnorePrice     float64                `json:"ignore_price"`
	User            string                 `json:"user"`
	UserID          int64                  `json:"user_id"`
	Remarks         string                 `json:"remarks"`
	CreatedAt       *string                `json:"created_at"`
	CashierID       int64                  `json:"cashier_id"`       //结账人id
	WechatUserID    int64                  `json:"wechat_user_id"`   //微信用户id
	CashierName     string                 `json:"cashier_name"`     //结账人
	GiveChange      float64                `json:"give_change"`      //找零金额
	CollectedAmount float64                `json:"collected_amount"` //收款金额
	OrderDetails    []*OrderDetailResource `json:"order_details" gorm:"-"`
	CanceledFoods   []*OrderDetailResource `json:"canceled_foods" gorm:"-"`
}

func (rc *OrderResource) Make(ctx context.Context, item *model.CloudOrderModel) *OrderResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(&ctx)
	data := OrderResource{
		ID:              item.ID,
		No:              item.No,
		TableID:         item.TableID,
		CustomersCount:  item.CustomersCount,
		FoodsCount:      item.FoodsCount,
		OriginalPrice:   item.OriginalPrice,
		VipPrice:        item.VipPrice,
		Price:           item.Price,
		IgnorePrice:     item.IgnorePrice,
		UserID:          item.UserID,
		Remarks:         item.Remarks,
		CreatedAt:       util.FormatDateTime(item.CreatedAt),
		CashierID:       item.CashierID,
		WechatUserID:    item.WechatUserID,
		GiveChange:      item.GiveChange,
		CollectedAmount: item.CollectedAmount,
		User:            "",
		OrderDetails:    []*OrderDetailResource{},
		CanceledFoods:   []*OrderDetailResource{},
	}
	if item.Creator != nil {
		data.User = item.Creator.NameUg
		if isZh {
			data.User = item.Creator.NameZh
		}
	}
	orderDetailResource := OrderDetailResource{}
	if item.Details != nil {
		for _, detail := range item.Details {
			if detail.State == 4 {
				data.CanceledFoods = append(data.CanceledFoods, orderDetailResource.Make(ctx, detail))
			} else {
				data.OrderDetails = append(data.OrderDetails, orderDetailResource.Make(ctx, detail))
			}
		}
	}
	return &data
}

func (rc *OrderResource) Collection(ctx context.Context, items []*model.CloudOrderModel) []*OrderResource {

	if items == nil || len(items) == 0 {
		return []*OrderResource{}
	}
	data := make([]*OrderResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

type OrderDetailResource struct {
	ID             int64                        `json:"id"`
	OrderDetailsID int64                        `json:"order_details_id"` //退菜订单的原始订单id
	MerchantNo     string                       `json:"merchant_no"`
	OrderID        int64                        `json:"order_id"`
	FoodID         int64                        `json:"food_id"`
	FoodsCount     float64                      `json:"foods_count"`
	CostPrice      float64                      `json:"cost_price"`
	OriginalPrice  float64                      `json:"original_price"`
	VipPrice       float64                      `json:"vip_price"`
	Price          float64                      `json:"price"`
	TotalPrice     float64                      `json:"total_price"`
	IsPrint        bool                         `json:"is_print"`
	User           string                       `json:"user"`
	UserID         int64                        `json:"user_id"`
	State          int                          `json:"state"` //订单状态 - 1 新订单（人数确定，未点菜）、2 未支付订单（已点菜，订单已提交）、3 已结账订单（已结账）、4 退菜、5 取消退菜 、6 被并单
	Remarks        *string                      `json:"remarks"`
	IsCombo        bool                         `json:"is_combo"`
	ComboInfo      util.TArray[model.ComboInfo] `json:"combo_info" gorm:"type:text"`
	IsSync         bool                         `json:"is_sync"`
	CreatedAt      *string                      `json:"created_at"`
	UpdatedAt      *string                      `json:"updated_at"`

	FoodNameUg   string `json:"food_name_ug"`
	FoodNameZh   string `json:"food_name_zh"`
	FoodFormatID int    `json:"food_format_id"`
}

func (rc *OrderDetailResource) Make(ctx context.Context, item *model.OrderDetailModel) *OrderDetailResource {
	if item == nil {
		return nil
	}
	isZh := i18n.IsZh(&ctx)
	data := OrderDetailResource{
		ID:             item.ID,
		OrderDetailsID: 0,
		MerchantNo:     item.MerchantNo,
		OrderID:        item.OrderID,
		FoodID:         item.FoodID,
		FoodsCount:     item.FoodsCount,
		CostPrice:      item.CostPrice,
		OriginalPrice:  item.OriginalPrice,
		VipPrice:       item.VipPrice,
		Price:          item.Price,
		TotalPrice:     item.TotalPrice,
		IsPrint:        item.IsPrint,
		User:           "",
		UserID:         item.UserID,
		State:          item.State,
		Remarks:        item.Remarks,
		IsCombo:        item.IsCombo,
		IsSync:         false,
		CreatedAt:      util.FormatDateTime(item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(item.UpdatedAt),

		FoodNameUg:   "",
		FoodNameZh:   "",
		FoodFormatID: 0,
	}
	if item.Creator != nil {
		data.User = item.Creator.NameUg
		if isZh {
			data.User = item.Creator.NameZh
		}
	}
	if item.Food != nil {
		data.FoodNameUg = item.Food.NameUg
		data.FoodNameZh = item.Food.NameZh
		data.FoodFormatID = item.Food.FormatID
	}
	return &data
}

func (rc *OrderDetailResource) Collection(ctx context.Context, items []*model.OrderDetailModel) []*OrderDetailResource {

	if items == nil || len(items) == 0 {
		return []*OrderDetailResource{}
	}
	data := make([]*OrderDetailResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}
