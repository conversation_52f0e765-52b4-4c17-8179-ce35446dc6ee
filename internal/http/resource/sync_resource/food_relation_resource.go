package sync_resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"
)

// FoodMethodResource 美食做法关联资源
type FoodMethodResource struct {
	ID         int64     `json:"id"`          // ID
	MerchantNo string    `json:"merchant_no"` // 商家编号
	FoodID     int64     `json:"food_id"`     // 美食ID
	GroupID    int64     `json:"group_id"`    // 做法分组ID
	MethodID   int64     `json:"method_id"`   // 做法ID
	Price      float64   `json:"price"`       // 做法价格
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间
}

// Make 转换单个模型为资源
func (r *FoodMethodResource) Make(foodMethod *model.FoodMethodModel) *FoodMethodResource {
	resource := &FoodMethodResource{
		ID:         foodMethod.ID,
		MerchantNo: foodMethod.MerchantNo,
		FoodID:     foodMethod.FoodID,
		GroupID:    foodMethod.GroupID,
		MethodID:   foodMethod.MethodID,
		CreatedAt:  foodMethod.CreatedAt,
		UpdatedAt:  foodMethod.UpdatedAt,
	}
	return resource
}

// Collection 转换模型集合为资源集合
func (r *FoodMethodResource) Collection(foodMethods []*model.FoodMethodModel) []*FoodMethodResource {
	resources := make([]*FoodMethodResource, 0, len(foodMethods))
	for _, foodMethod := range foodMethods {
		resources = append(resources, r.Make(foodMethod))
	}
	return resources
}

// FoodLunchBoxResource 餐盒关联关系资源
type FoodLunchBoxResource struct {
	ID             int64   `json:"id"`               // ID
	MerchantNo     string  `json:"merchant_no"`      // 商家编号
	FoodID         int64   `json:"food_id"`          // 美食ID
	FoodCategoryID int64   `json:"food_category_id"` // 美食分类ID
	LunchBoxID     int64   `json:"lunch_box_id"`     // 餐盒ID
	Count          int64   `json:"count"`            // 餐盒数量
	CreatedAt      *string `json:"created_at"`       // Create time
	UpdatedAt      *string `json:"updated_at"`       // Update time
}

func (rc *FoodLunchBoxResource) Make(item *model.FoodLunchBoxModel) *FoodLunchBoxResource {
	if item == nil {
		return nil
	}
	data := FoodLunchBoxResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		FoodID:     item.FoodID,
		LunchBoxID: item.LunchBoxID,
		Count:      item.Count,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
	}
	return &data
}

func (rc *FoodLunchBoxResource) Collection(items []*model.FoodLunchBoxModel) []*FoodLunchBoxResource {
	if items == nil || len(items) == 0 {
		return []*FoodLunchBoxResource{}
	}
	data := make([]*FoodLunchBoxResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

// FoodAdditionFoodsResource 加料关联关系资源
type FoodAdditionFoodsResource struct {
	ID         int64   `json:"id"`          // ID
	MerchantNo string  `json:"merchant_no"` // 商家编号
	FoodID     int64   `json:"food_id"`     // 美食ID
	CategoryID int64   `json:"category_id"` // 美食分类ID
	AdditionID int64   `json:"addition_id"` // 加料ID
	Price      float64 `json:"price"`       // 加料价格
	CreatedAt  *string `json:"created_at"`  // Create time
	UpdatedAt  *string `json:"updated_at"`  // Update time
}

func (rc *FoodAdditionFoodsResource) Make(item *model.FoodAdditionModel) *FoodAdditionFoodsResource {
	if item == nil {
		return nil
	}
	data := FoodAdditionFoodsResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		FoodID:     item.FoodID,
		CategoryID: item.CategoryID,
		AdditionID: item.AdditionID,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
	}

	return &data
}

func (rc *FoodAdditionFoodsResource) Collection(items []*model.FoodAdditionModel) []*FoodAdditionFoodsResource {
	if items == nil || len(items) == 0 {
		return []*FoodAdditionFoodsResource{}
	}
	data := make([]*FoodAdditionFoodsResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}
