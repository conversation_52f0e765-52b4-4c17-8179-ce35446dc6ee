package food_resource

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// LunchBoxResource 餐盒资源
type LunchBoxResource struct {
	ID               int64                 `json:"id"`                 // Unique ID
	MerchantNo       string                `json:"merchant_no"`        // 商家编号
	FoodCategoryID   int64                 `json:"food_category_id"`   // 餐盒分类ID
	Image            string                `json:"image"`              // 图片
	ShortcutCode     string                `json:"shortcut_code"`      // 快捷码
	NameUg           string                `json:"name_ug"`            // 名称(维语)
	NameZh           string                `json:"name_zh"`            // 名称(中文)
	CostPrice        float64               `json:"cost_price"`         // 定价
	VipPrice         float64               `json:"vip_price"`          // 会员价
	Price            float64               `json:"price"`              // 现价
	FormatID         int                   `json:"format_id"`          // 计费方式
	IsSpecialFood    bool                  `json:"is_special_food"`    // 是否特色菜
	SupportScanOrder bool                  `json:"support_scan_order"` // 是否支持扫码点单
	Sort             int64                 `json:"sort"`               // 排序
	State            int64                 `json:"state"`              // 状态
	FoodCategory     *FoodCategoryResource `json:"food_category"`      // 美食分类
	AssociatedFoods  int64                 `json:"associated_foods"`   // 关联的菜品数量
	CreatedAt        *string               `json:"created_at"`         // Create time
	UpdatedAt        *string               `json:"updated_at"`         // Update time
	DeletedAt        *string               `json:"deleted_at"`         // Delete time
}

func (rc *LunchBoxResource) Make(ctx *context.Context, item *model.FoodModel) *LunchBoxResource {
	if item == nil {
		return nil
	}
	data := LunchBoxResource{
		ID:               item.ID,
		MerchantNo:       item.MerchantNo,
		FoodCategoryID:   item.FoodCategoryID,
		Image:            item.Image,
		ShortcutCode:     item.ShortcutCode,
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FormatID:         item.FormatID,
		IsSpecialFood:    item.IsSpecialFood,
		SupportScanOrder: item.SupportScanOrder,
		Sort:             item.Sort,
		State:            item.State,
		CreatedAt:        util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:        util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:        util.FormatDateTime(item.DeletedAt),
	}

	// 处理分类信息
	if item.FoodCategory != nil {
		data.FoodCategory = (&FoodCategoryResource{}).Make(ctx, item.FoodCategory)
	}

	return &data
}

func (rc *LunchBoxResource) Collection(ctx *context.Context, items []*model.FoodModel) []*LunchBoxResource {
	if items == nil || len(items) == 0 {
		return []*LunchBoxResource{}
	}
	data := make([]*LunchBoxResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}

// FoodLunchBoxResource 餐盒关联关系资源
type FoodLunchBoxResource struct {
	ID             int64             `json:"id"`               // ID
	MerchantNo     string            `json:"merchant_no"`      // 商家编号
	FoodID         int64             `json:"food_id"`          // 美食ID
	NameZh         string            `json:"name_zh"`          // 名称(中文)
	NameUg         string            `json:"name_ug"`          // 名称(维语)
	FoodCategoryID int64             `json:"food_category_id"` // 美食分类ID
	CategoryNameZh string            `json:"category_name_zh"` // 美食分类名称(中文)
	CategoryNameUg string            `json:"category_name_ug"` // 美食分类名称(维语)
	LunchBoxID     int64             `json:"lunch_box_id"`     // 餐盒ID
	Count          int64             `json:"count"`            // 餐盒数量
	Price          float64           `json:"price"`            // 餐盒价格
	IsSurcharge    bool              `json:"is_surcharge"`     // 是否加价
	Food           *FoodResource     `json:"food"`             // 关联美食
	LunchBox       *LunchBoxResource `json:"lunch_box"`        // 关联餐盒
	CreatedAt      *string           `json:"created_at"`       // Create time
	UpdatedAt      *string           `json:"updated_at"`       // Update time
}

func (rc *FoodLunchBoxResource) Make(ctx *context.Context, item *model.FoodLunchBoxModel) *FoodLunchBoxResource {
	if item == nil {
		return nil
	}
	data := FoodLunchBoxResource{
		ID:          item.ID,
		MerchantNo:  item.MerchantNo,
		FoodID:      item.FoodID,
		LunchBoxID:  item.LunchBoxID,
		Count:       item.Count,
		IsSurcharge: item.Count > 0, // 价格大于0表示加价
		CreatedAt:   util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:   util.FormatDateTime(&item.UpdatedAt),
	}

	// 处理关联信息
	if item.Food != nil {
		data.FoodCategoryID = item.Food.FoodCategoryID
		data.NameZh = item.Food.NameZh
		data.NameUg = item.Food.NameUg
		data.Price = item.Food.Price
		if item.Food.FoodCategory != nil {
			data.CategoryNameZh = item.Food.FoodCategory.NameZh
			data.CategoryNameUg = item.Food.FoodCategory.NameUg
		}
	}
	if item.LunchBox != nil {
		data.LunchBox = (&LunchBoxResource{}).Make(ctx, item.LunchBox)
	}

	return &data
}

func (rc *FoodLunchBoxResource) Collection(ctx *context.Context, items []*model.FoodLunchBoxModel) []*FoodLunchBoxResource {
	if items == nil || len(items) == 0 {
		return []*FoodLunchBoxResource{}
	}
	data := make([]*FoodLunchBoxResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}
