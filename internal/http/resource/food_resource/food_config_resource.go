package food_resource

import (
	"ros-api-go/internal/model"
)

type FoodConfigResource struct {
	ID            int64   `json:"id"`                      // ID
	MerchantNo    string  `json:"merchant_no"`             // 商户编号
	FoodID        int64   `json:"food_id"`                 // 菜品ID
	GroupID       int64   `json:"group_id"`                // 分组ID
	Required      bool    `json:"required"`                // 是否必选
	RequiredCount float64 `json:"required_count"`          // 必选数量
	UpperLimit    bool    `json:"upper_limit"`             // 是否限制最多可选数量
	UpperCount    float64 `json:"upper_count"`             // 最多可选数量
	RepeatChoice  bool    `json:"repeat_choice,omitempty"` // 是否可重复选择
}

// Make 将食品模型转换为资源
func (rc *FoodConfigResource) Make(item *model.FoodConfigModel) *FoodConfigResource {
	if item == nil {
		return nil
	}

	return &FoodConfigResource{
		ID:            item.ID,
		MerchantNo:    item.MerchantNo,
		FoodID:        item.FoodID,
		GroupID:       item.GroupID,
		Required:      item.Required,
		RequiredCount: item.RequiredCount,
		UpperLimit:    item.UpperLimit,
		UpperCount:    item.UpperCount,
		RepeatChoice:  item.RepeatChoice,
	}
}
