package food_resource

import (
	"ros-api-go/internal/model"
)

type FoodPrinterResource struct {
	FoodID    int64  `json:"food_id"`
	PrinterID string `json:"printer_id"`
}

func (rc *FoodPrinterResource) Make(item *model.FoodPrinterModel) *FoodPrinterResource {
	if item == nil {
		return nil
	}
	data := FoodPrinterResource{
		FoodID:    item.FoodID,
		PrinterID: item.PrinterID,
	}
	return &data
}

func (rc *FoodPrinterResource) Collection(items []*model.FoodPrinterModel) []*FoodPrinterResource {

	if items == nil || len(items) == 0 {
		return []*FoodPrinterResource{}
	}
	data := make([]*FoodPrinterResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}
