package food_resource

import (
	"context"
	"ros-api-go/internal/model"
)

type FoodAdditionGroupResource struct {
	ID         int64           `json:"id"`          // Unique ID
	MerchantNo string          `json:"merchant_no"` // 商家编号
	NameUg     string          `json:"name_ug"`     // 名称(维语)
	NameZh     string          `json:"name_zh"`     // 名称(中文)
	Sort       int64           `json:"sort"`        // 排序
	FoodsCount int             `json:"foods_count"` // 菜品数量
	Foods      []*FoodResource `json:"foods"`       // 菜品列表
}

func (rc *FoodAdditionGroupResource) Make(ctx *context.Context, item *model.FoodCategoryModel) *FoodAdditionGroupResource {
	if item == nil {
		return nil
	}
	data := FoodAdditionGroupResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		Sort:       item.Sort,
		FoodsCount: item.FoodsCount,
	}
	data.Foods = (&FoodResource{}).Collection(ctx, item.Foods)
	return &data
}

func (rc *FoodAdditionGroupResource) Collection(ctx *context.Context, items []*model.FoodCategoryModel) []*FoodAdditionGroupResource {

	if items == nil || len(items) == 0 {
		return []*FoodAdditionGroupResource{}
	}
	data := make([]*FoodAdditionGroupResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}
