package food_resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// FoodSpecResource 规格资源
type FoodSpecResource struct {
	ID         int64  `json:"id"`          // Unique ID
	MerchantNo string `json:"merchant_no"` // 商家编号
	NameZh     string `json:"name_zh"`     // 规格名称(中文)
	NameUg     string `json:"name_ug"`     // 规格名称(维语)
	Sort       int64  `json:"sort"`        // 排序
	IsDefault  bool   `json:"is_default"`  // 是否默认规格
	FoodsCount int64  `json:"foods_count"` // 关联美食数量
	CreatedAt  string `json:"created_at"`  // 创建时间
	UpdatedAt  string `json:"updated_at"`  // 更新时间
}

// Make 转换单个规格模型为资源
func (rc *FoodSpecResource) Make(item *model.FoodSpecModel) *FoodSpecResource {
	if item == nil {
		return nil
	}

	return &FoodSpecResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		NameZh:     item.NameZh,
		NameUg:     item.NameUg,
		Sort:       item.Sort,
		IsDefault:  item.IsDefault,
		FoodsCount: item.FoodsCount,
		CreatedAt:  *util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  *util.FormatDateTime(&item.UpdatedAt),
	}
}

// Collection 转换规格模型集合为资源集合
func (rc *FoodSpecResource) Collection(items []*model.FoodSpecModel) []*FoodSpecResource {
	if items == nil || len(items) == 0 {
		return []*FoodSpecResource{}
	}

	data := make([]*FoodSpecResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}
