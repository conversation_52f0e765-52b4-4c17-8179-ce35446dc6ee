package food_resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// MethodGroupResource 做法分组资源
type MethodGroupResource struct {
	ID           int64   `json:"id"`            // ID
	NameZh       string  `json:"name_zh"`       // 分组名称(中文)
	NameUg       string  `json:"name_ug"`       // 分组名称(维语)
	Sort         int64   `json:"sort"`          // 排序
	MethodsCount int64   `json:"methods_count"` // 做法数量
	CreatedAt    *string `json:"created_at"`    // 创建时间
	UpdatedAt    *string `json:"updated_at"`    // 更新时间

	Methods []*MethodResource `json:"methods"` // 做法
}

// Make 转换单个模型为资源
func (r *MethodGroupResource) Make(methodGroup *model.MethodGroupModel) *MethodGroupResource {
	data := &MethodGroupResource{
		ID:           methodGroup.ID,
		NameZh:       methodGroup.NameZh,
		NameUg:       methodGroup.NameUg,
		Sort:         methodGroup.Sort,
		MethodsCount: methodGroup.MethodsCount,
		CreatedAt:    util.FormatDateTime(&methodGroup.CreatedAt),
		UpdatedAt:    util.FormatDateTime(&methodGroup.UpdatedAt),
	}
	if methodGroup.Methods != nil && len(methodGroup.Methods) > 0 {
		data.Methods = (&MethodResource{}).Collection(methodGroup.Methods)
	}
	return data
}

// Collection 转换模型集合为资源集合
func (r *MethodGroupResource) Collection(methodGroups []*model.MethodGroupModel) []*MethodGroupResource {
	resources := make([]*MethodGroupResource, 0, len(methodGroups))
	for _, methodGroup := range methodGroups {
		resources = append(resources, r.Make(methodGroup))
	}
	return resources
}
