package food_resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// FoodSpecFoodsResource 规格关联美食资源
type FoodSpecFoodsResource struct {
	ID               int64   `json:"id"`                 // 美食ID
	MerchantNo       string  `json:"merchant_no"`        // 商家编号
	Pid              int64   `json:"pid"`                // 上级美食ID
	NameZh           string  `json:"name_zh"`            // 上级美食名称(中文)
	NameUg           string  `json:"name_ug"`            // 上级美食名称(维语)
	SpecID           int64   `json:"spec_id"`            // 规格ID
	FoodCategoryID   int64   `json:"food_category_id"`   // 分类ID
	CategoryNameZh   string  `json:"category_name_zh"`   // 分类名称(中文)
	CategoryNameUg   string  `json:"category_name_ug"`   // 分类名称(维语)
	ShortcutCode     string  `json:"shortcut_code"`      // 快捷码
	SpecNameUg       string  `json:"spec_name_ug"`       // 名称(维语)
	SpecNameZh       string  `json:"spec_name_zh"`       // 名称(中文)
	CostPrice        float64 `json:"cost_price"`         // 定价
	VipPrice         float64 `json:"vip_price"`          // 会员价格
	Price            float64 `json:"price"`              // 现价
	FormatID         int     `json:"format_id"`          // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`    // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"` // 是否支持扫码点单
	Sort             int64   `json:"sort"`               // 排序
	State            int64   `json:"state"`              // 状态
	CreatedAt        *string `json:"created_at"`         // 创建时间
	UpdatedAt        *string `json:"updated_at"`         // 更新时间
}

// Make 转换单个美食模型为资源
func (rc *FoodSpecFoodsResource) Make(item *model.FoodModel, ParentFood *model.FoodModel) *FoodSpecFoodsResource {
	if item == nil {
		return nil
	}

	resource := &FoodSpecFoodsResource{
		ID:             item.ID,
		MerchantNo:     item.MerchantNo,
		Pid:            item.Pid,
		SpecID:         item.SpecID,
		FoodCategoryID: item.FoodCategoryID,
		ShortcutCode:   item.ShortcutCode,
		SpecNameUg:     item.NameUg,
		SpecNameZh:     item.NameZh,
		CostPrice:      item.CostPrice,
		VipPrice:       item.VipPrice,
		Price:          item.Price,
		FormatID:       item.FormatID,
		Sort:           item.Sort,
		State:          item.State,
		CreatedAt:      util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:      util.FormatDateTime(&item.UpdatedAt),
	}

	// 设置上级美食信息
	if ParentFood != nil {
		resource.NameZh = ParentFood.NameZh
		resource.NameUg = ParentFood.NameUg
	}

	// 设置分类信息
	if item.FoodCategory != nil {
		resource.CategoryNameZh = item.FoodCategory.NameZh
		resource.CategoryNameUg = item.FoodCategory.NameUg
	}

	return resource
}

// Collection 转换美食模型集合为资源集合
func (rc *FoodSpecFoodsResource) Collection(items []*model.FoodModel) []*FoodSpecFoodsResource {
	if items == nil || len(items) == 0 {
		return []*FoodSpecFoodsResource{}
	}

	data := make([]*FoodSpecFoodsResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item, item.ParentFood)
	}
	return data
}
