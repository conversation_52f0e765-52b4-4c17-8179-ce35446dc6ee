package food_resource

import (
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// FoodAdditionResource 加料资源
type FoodAdditionResource struct {
	ID               int64   `json:"id"`                 // Unique ID
	MerchantNo       string  `json:"merchant_no"`        // 商家编号
	FoodCategoryID   int64   `json:"food_category_id"`   // 加料分类ID
	Image            string  `json:"image"`              // 图片
	ShortcutCode     string  `json:"shortcut_code"`      // 快捷码
	NameUg           string  `json:"name_ug"`            // 名称(维语)
	NameZh           string  `json:"name_zh"`            // 名称(中文)
	CategoryNameZh   string  `json:"category_name_zh"`   // 分类名称(中文)
	CategoryNameUg   string  `json:"category_name_ug"`   // 分类名称(维语)
	CostPrice        float64 `json:"cost_price"`         // 定价
	VipPrice         float64 `json:"vip_price"`          // 会员价
	Price            float64 `json:"price"`              // 现价
	FormatID         int     `json:"format_id"`          // 计费方式
	IsSpecialFood    bool    `json:"is_special_food"`    // 是否特色菜
	SupportScanOrder bool    `json:"support_scan_order"` // 是否支持扫码点单
	Sort             int64   `json:"sort"`               // 排序
	State            int64   `json:"state"`              // 状态
	AssociatedFoods  int64   `json:"associated_foods"`   // 关联的菜品数量
	CreatedAt        *string `json:"created_at"`         // Create time
	UpdatedAt        *string `json:"updated_at"`         // Update time
	DeletedAt        *string `json:"deleted_at"`         // Delete time
}

func (rc *FoodAdditionResource) Make(item *model.FoodModel) *FoodAdditionResource {
	if item == nil {
		return nil
	}
	data := FoodAdditionResource{
		ID:               item.ID,
		MerchantNo:       item.MerchantNo,
		FoodCategoryID:   item.FoodCategoryID,
		Image:            config.C.Storage.OSS.Endpoint + item.Image,
		ShortcutCode:     item.ShortcutCode,
		NameUg:           item.NameUg,
		NameZh:           item.NameZh,
		CostPrice:        item.CostPrice,
		VipPrice:         item.VipPrice,
		Price:            item.Price,
		FormatID:         item.FormatID,
		IsSpecialFood:    item.IsSpecialFood,
		SupportScanOrder: item.SupportScanOrder,
		Sort:             item.Sort,
		State:            item.State,
		CreatedAt:        util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:        util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:        util.FormatDateTime(item.DeletedAt),
	}

	// 处理分类信息
	if item.FoodCategory != nil {
		data.CategoryNameZh = item.FoodCategory.NameZh
		data.CategoryNameUg = item.FoodCategory.NameUg
	}

	return &data
}

func (rc *FoodAdditionResource) Collection(items []*model.FoodModel) []*FoodAdditionResource {
	if items == nil || len(items) == 0 {
		return []*FoodAdditionResource{}
	}
	data := make([]*FoodAdditionResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}

// FoodAdditionFoodsResource 加料关联关系资源
type FoodAdditionFoodsResource struct {
	ID             int64   `json:"id"`               // ID
	MerchantNo     string  `json:"merchant_no"`      // 商家编号
	FoodID         int64   `json:"food_id"`          // 美食ID
	NameZh         string  `json:"name_zh"`          // 上级美食名称(中文)
	NameUg         string  `json:"name_ug"`          // 上级美食名称(维语)
	CategoryID     int64   `json:"category_id"`      // 分组ID
	CategoryNameZh string  `json:"category_name_zh"` // 分类名称(中文)
	CategoryNameUg string  `json:"category_name_ug"` // 分类名称(维语)
	AdditionID     int64   `json:"addition_id"`      // 加料ID
	CreatedAt      *string `json:"created_at"`       // Create time
	UpdatedAt      *string `json:"updated_at"`       // Update time
}

func (rc *FoodAdditionFoodsResource) Make(item *model.FoodAdditionModel) *FoodAdditionFoodsResource {
	if item == nil {
		return nil
	}
	data := FoodAdditionFoodsResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		FoodID:     item.FoodID,
		CategoryID: item.CategoryID,
		AdditionID: item.AdditionID,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
	}

	// 处理关联信息
	if item.Food != nil {
		data.NameZh = item.Food.NameZh
		data.NameUg = item.Food.NameUg
		// 处理美食分类信息
		if item.Food.FoodCategory != nil {
			data.CategoryNameZh = item.Food.FoodCategory.NameZh
			data.CategoryNameUg = item.Food.FoodCategory.NameUg
		}
	}

	return &data
}

func (rc *FoodAdditionFoodsResource) Collection(items []*model.FoodAdditionModel) []*FoodAdditionFoodsResource {
	if items == nil || len(items) == 0 {
		return []*FoodAdditionFoodsResource{}
	}
	data := make([]*FoodAdditionFoodsResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}
