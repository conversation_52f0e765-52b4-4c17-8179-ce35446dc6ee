package food_resource

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type FoodCategoryResource struct {
	ID         int64   `json:"id"`          // Unique ID
	MerchantNo string  `json:"merchant_no"` // 商家编号
	Type       int64   `json:"type"`        // 分类类型: 1美食分类 2 加料分类 3 饭盒
	Name       string  `json:"name"`        // 名称
	NameUg     string  `json:"name_ug"`     // 名称(维语)
	NameZh     string  `json:"name_zh"`     // 名称(中文)
	Sort       int64   `json:"sort"`        // 排序
	State      int64   `json:"state"`       // 状态
	FoodsCount int     `json:"foods_count"` // 菜品数量
	CreatedAt  *string `json:"created_at"`  // Create time
	UpdatedAt  *string `json:"updated_at"`  // Update time
	DeletedAt  *string `json:"deleted_at"`  // Delete time

	Foods []*FoodResource `json:"foods"` // 菜品列表
}

type FoodCategorySimpleResource struct {
	ID     int64           `json:"id"`      // Unique ID
	Type   int64           `json:"type"`    // 分类类型: 1美食分类 2 加料分类 3 饭盒
	NameUg string          `json:"name_ug"` // 名称(维语)
	NameZh string          `json:"name_zh"` // 名称(中文)
	Foods  []*FoodResource `json:"foods"`   // 菜品列表
}

func (rc *FoodCategoryResource) Make(ctx *context.Context, item *model.FoodCategoryModel) *FoodCategoryResource {
	if item == nil {
		return nil
	}
	data := FoodCategoryResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		Type:       item.Type,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		Sort:       item.Sort,
		State:      item.State,
		FoodsCount: item.FoodsCount,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:  util.FormatDateTime(item.DeletedAt),
	}

	isZh := i18n.IsZh(ctx)
	data.Name = data.NameUg
	if isZh {
		data.Name = data.NameZh
	}

	if item.Foods != nil {
		foodResource := FoodResource{}
		data.Foods = foodResource.Collection(ctx, item.Foods)
	}
	return &data
}

func (rc *FoodCategoryResource) Collection(ctx *context.Context, items []*model.FoodCategoryModel) []*FoodCategoryResource {

	if items == nil || len(items) == 0 {
		return []*FoodCategoryResource{}
	}
	data := make([]*FoodCategoryResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(ctx, item)
	}
	return data
}
