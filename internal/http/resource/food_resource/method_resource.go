package food_resource

import (
	"ros-api-go/internal/config"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// MethodResource 做法资源
type MethodResource struct {
	ID          int64   `json:"id"`            // ID
	MerchantNo  string  `json:"merchant_no"`   // 商家编号
	GroupID     int64   `json:"group_id"`      // 做法分组ID
	GroupNameZh string  `json:"group_name_zh"` // 做法分组名称(中文)
	GroupNameUg string  `json:"group_name_ug"` // 做法分组名称(维语)
	NameZh      string  `json:"name_zh"`       // 做法名称(中文)
	NameUg      string  `json:"name_ug"`       // 做法名称(维语)
	DescZh      string  `json:"desc_zh"`       // 做法描述(中文)
	DescUg      string  `json:"desc_ug"`       // 做法描述(维语)
	Price       float64 `json:"price"`         // 价格
	IsSurcharge bool    `json:"is_surcharge"`  // 是否加价
	Sort        int64   `json:"sort"`          // 排序
	FoodsCount  int64   `json:"foods_count"`   // 关联美食数量
	CreatedAt   *string `json:"created_at"`    // 创建时间
	UpdatedAt   *string `json:"updated_at"`    // 更新时间
}

// Make 转换单个模型为资源
func (r *MethodResource) Make(method *model.MethodModel) *MethodResource {
	resource := &MethodResource{
		ID:          method.ID,
		MerchantNo:  method.MerchantNo,
		GroupID:     method.GroupID,
		NameZh:      method.NameZh,
		NameUg:      method.NameUg,
		DescZh:      method.DescZh,
		DescUg:      method.DescUg,
		Price:       method.Price,
		IsSurcharge: method.Price > 0,
		Sort:        method.Sort,
		FoodsCount:  method.FoodsCount,
		CreatedAt:   util.FormatDateTime(&method.CreatedAt),
		UpdatedAt:   util.FormatDateTime(&method.UpdatedAt),
	}

	// 转换分组信息
	if method.Group != nil {
		resource.GroupNameZh = method.Group.NameZh
		resource.GroupNameUg = method.Group.NameUg
	}

	return resource
}

// Collection 转换模型集合为资源集合
func (r *MethodResource) Collection(methods []*model.MethodModel) []*MethodResource {
	resources := make([]*MethodResource, 0, len(methods))
	for _, method := range methods {
		resources = append(resources, r.Make(method))
	}
	return resources
}

// MethodFoodResource 做法关联美食资源
type MethodFoodResource struct {
	ID             int64  `json:"id"`               // 美食ID
	NameZh         string `json:"name_zh"`          // 美食名称(中文)
	NameUg         string `json:"name_ug"`          // 美食名称(维语)
	FoodCategoryID int64  `json:"food_category_id"` // 分类ID
	CategoryNameZh string `json:"category_name_zh"` // 分类名称(中文)
	CategoryNameUg string `json:"category_name_ug"` // 分类名称(维语)
	Image          string `json:"image"`            // 美食图片
	GroupID        int64  `json:"group_id"`         // 分组ID
}

// Make 转换美食做法关联为资源
func (r *MethodFoodResource) Make(foodMethod *model.FoodMethodModel) *MethodFoodResource {
	food := foodMethod.Food
	data := &MethodFoodResource{
		ID:             food.ID,
		NameZh:         food.NameZh,
		NameUg:         food.NameUg,
		FoodCategoryID: food.FoodCategoryID,
		Image:          config.C.Storage.OSS.Endpoint + food.Image,
		GroupID:        foodMethod.GroupID,
	}
	if food.FoodCategory != nil {
		data.CategoryNameUg = food.FoodCategory.NameUg
		data.CategoryNameZh = food.FoodCategory.NameZh
	}
	return data
}

// Collection 转换美食做法关联集合为资源集合
func (r *MethodFoodResource) Collection(foodMethods []*model.FoodMethodModel) []*MethodFoodResource {
	resources := make([]*MethodFoodResource, 0, len(foodMethods))
	for _, fm := range foodMethods {
		if fm.Food != nil {
			resources = append(resources, r.Make(fm))
		}
	}
	return resources
}
