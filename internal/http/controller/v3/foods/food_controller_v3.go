package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource/order_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type FoodControllerV3 struct {
	FoodsService *food_service.FoodsService
}

// GetMerchantAvailableFoods 获取商家正常的美食列表
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 获取商家正常的美食列表
// @Produce json
// @Param food_category_id query int false "食物类别ID"
// @Param keyword query string false "搜索关键词"
// @Param sell_clear_all query string false "是否清理所有销售标志"
// @Success 200 {object} util.ResponseResult{ Data=[]food_resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodControllerV3) GetMerchantAvailableFoods(c *gin.Context) {
	ctx := c.Request.Context()
	MerchantNo := util.GetMerchantNo(c)

	// 从查询参数中获取食物类别ID
	catId := util.StrToInt64(c.Query("food_category_id"))
	// 从查询参数中获取搜索关键词
	keyword := c.Query("keyword")
	// 从查询参数中获取是否清理所有销售标志
	sellClearAll := c.Query("sell_clear_all")

	result, err := ctrl.FoodsService.GetMerchantAvailableFoodsV2(ctx, MerchantNo, catId, keyword, sellClearAll)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&order_resource.FoodResourceV2{}).Collection(&ctx, result))
}
