package v2

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/merchant_employee_request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/crypto/hash"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
)

type OperationPasswordController struct {
	UserService               *service.UserService
	MerchantEmployeeService   *service.MerchantEmployeeService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

func (ctrl *OperationPasswordController) SendOperationPasswordSmsCode(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)

	user, err := ctrl.UserService.MustGetByUserID(ctx, userID)

	if err != nil {
		util.ResError(c, err)
		return
	}

	batchID, err := sms.SendOperationPasswordSMS(ctx, user.Phone)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, map[string]string{"batchID": batchID})
}

func (ctrl *OperationPasswordController) SetOperationPassword(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	userID := util.FromUserID(ctx)
	formItem := merchant_employee_request.SetOperationPasswordRequest{}
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	employee, err := ctrl.MerchantEmployeeService.MustGetByUserID(ctx, merchantNo, userID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 验证密码
	if err := formItem.Validate(ctx); err != nil {
		util.ResError(c, err)
		return
	}

	// 验证密码是否与原密码相同
	err = hash.CompareHashAndPassword(employee.OperationPassword, formItem.Password)
	if err == nil {
		util.ResError(c, errors.BadRequest("", "PasswordCannotBeSameAsOriginal")) // 密码不能与原密码相同
		return
	}

	user, err := ctrl.UserService.MustGetByUserID(ctx, userID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 验证验证码
	result, err := sms.VerifySmsCode(ctx, user.Phone, formItem.BatchID, consts.CacheKeyForOperationPwdSMSCode, formItem.Code)
	if err != nil {
		logging.Context(ctx).Info("verify sms code fail",
			zap.String("phone", user.Phone),
			zap.String("batchID", formItem.BatchID),
			zap.String("code", formItem.Code),
		)
		util.ResError(c, errors.BadRequest("", "InvalidVerifyCode"))
		return
	}
	if result {
		// 设置密码
		err = ctrl.MerchantEmployeeService.SetOperationPassword(ctx, employee.ID, merchantNo, formItem.Password)
		if err != nil {
			util.ResError(c, err)
			return
		}
		util.ResOK(c, "Success")
		ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
		return
	}
	util.ResError(c, errors.BadRequest("", "InvalidVerifyCode"))
}

func (ctrl *OperationPasswordController) getEmployee(ctx context.Context, merchantNo string, userID int64) (*model.MerchantEmployeeModel, error) {
	employee, err := ctrl.MerchantEmployeeService.GetByUserID(ctx, merchantNo, userID)
	if err != nil {
		return nil, err
	}
	if employee == nil {
		return nil, errors.NotFound("", "UserNotFound")
	}
	if employee.State == model.EmployeeStateDeleted {
		return nil, errors.NotFound("", "UserNotActive")
	}
	return employee, nil
}
