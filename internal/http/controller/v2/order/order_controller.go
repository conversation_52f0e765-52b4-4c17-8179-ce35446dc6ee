package order

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"ros-api-go/internal/broadcast/order_broadcast"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/http/resource/order_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"strconv"
	"time"
)

type OrderController struct {
	OrderService       *service.OrderService
	OrderCloudService  *service.OrderCloudService
	PaymentTypeService *service.PaymentTypeService
	PaymentService     *service.PaymentService
	MerchantService    *service.MerchantService
	PaymentLogService  *service.PaymentLogService
	WechatPayService   *service.WechatPayService
	TableService       *service.TableService
	Trans              *util.Trans
	EmployeeService    *service.MerchantEmployeeService
	OrderSyncHandler   *handler.OrderSyncHandler
	PrintService       *service.PrintService
}

// TableList 餐台信息列表
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 餐台信息列表
// @Success 200 {object} util.ResponseResult{data=resource.TableDataResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/table [get]
// @Param area_id query int64 true "区域ID"
// @Param table_status query int64 true "状态"
// @Param keyword query string true "匹配"
// @X-Author ["Habibulla"]
// @X-Date ["2025/3/27 16:11"]
// @X-Version ["2.0"]
func (ctrl *OrderController) TableList(c *gin.Context) {
	ctx := c.Request.Context()

	//userID := util.FromUserID(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 从请求中获取查询参数
	area_id := c.Query("area_id")
	table_status := c.Query("table_status")
	keyword := c.Query("keyword")

	// 调用业务逻辑层的方法获取餐桌列表
	tablesData, EmptyTableCount, HasCustomersTableCount, HasOrderTableCount, OrderCount, OrderAmount, err := ctrl.OrderCloudService.TableList(ctx, merchantNo, area_id, table_status, keyword)

	// 构建并返回包含桌台列表和相关信息的结构体
	tables := &resource.TableDataResource{
		TableData: tablesData,
		TableInfo: resource.TableInfo{
			EmptyTableCount:        EmptyTableCount,
			HasCustomersTableCount: HasCustomersTableCount,
			HasOrderTableCount:     HasOrderTableCount,
		},
		OrderInfo: resource.OrderInfo{
			OrderCount:  OrderCount,
			OrderAmount: OrderAmount,
		},
	}
	if err != nil {
		// 如果发生错误，返回HTTP状态码400和错误信息
		util.ResError(c, errors.BadRequest("", "GetFailed"))
		return
	} else {
		// 如果查询成功，返回HTTP状态码200和查询结果
		util.ResSuccess(c, tables)
	}
}

// 创建订单
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 创建订单
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders [post]
// @Param customers_count query int64 true "客户人数"
// @Param table_id query int64 true "餐桌ID"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/2 10:37"]
// @X-Version ["2.0"]
func (ctrl *OrderController) CreateOrder(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 获取客户数量
	customersCount := c.Query("customers_count")
	// 获取桌号
	tableIdStr := c.Query("table_id")
	// 获取终端信息
	terminal := c.GetHeader("terminal")

	// 检查客户数量是否为空
	if customersCount == "" {
		util.ResError(c, errors.BadRequest("", "CustomerCountMissing"))
		return
	}
	// 检查桌号是否为空
	if tableIdStr == "" {
		util.ResError(c, errors.BadRequest("", "TableInfoMissing"))
		return
	}
	tableId, err2 := strconv.Atoi(tableIdStr)
	// 检查终端信息是否为空
	if err2 != nil {
		util.ResError(c, errors.BadRequest("", "TableInfoMissing"))
		return
	}
	customersCountInt, _ := strconv.Atoi(customersCount)
	// 创建订单
	order, err := ctrl.OrderCloudService.CreateOrder(ctx, merchantNo, userID, userName, customersCountInt, int64(tableId), terminal)

	if err == nil {
		// 发送订单创建广播
		order_broadcast.SendOrderBroadcast(order_broadcast.ActionAddOrder, order.MerchantNo, order.TableID, order.ID, nil)
		// 返回成功响应
		util.ResSuccess(c, order)
	} else {
		// 返回错误响应
		util.ResError(c, errors.BadRequest("", "CreateOrderFailed"))
	}
}

// 餐桌详情
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 餐桌详情
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/table-details/:id [get]
// @Param id path int64 true "餐桌ID"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/2 10:38"]
// @X-Version ["2.0"]
func (ctrl *OrderController) TableDetails(c *gin.Context) {
	ctx := c.Request.Context()
	// 从请求参数中提取订单ID。
	id := c.Param("id")
	merchantNo := c.Request.Header.Get("Merchantno")

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if merchant == nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}

	// 调用业务逻辑层方法，获取指定订单ID的详细订单信息。
	table, categoryModel, foodModel, err := ctrl.OrderCloudService.TableOrders(ctx, merchantNo, id)
	if err != nil {
		// 如果发生错误，使用工具方法返回错误响应。
		util.ResError(c, err)
		return
	}
	catResource := food_resource.FoodCategoryResource{}
	foodResource := food_resource.FoodResource{}
	tableResource := order_resource.TableResource{}
	tableDetails := order_resource.TablesDetailResource{
		Table:          tableResource.Make(ctx, table),
		FoodCategories: catResource.Collection(&ctx, categoryModel),
		Foods:          foodResource.Collection(&ctx, foodModel),
		DiancaiPay:     merchant.DiancaiPay,
	}

	types, err := ctrl.PaymentTypeService.GetMerchantPaymentTypes(ctx, merchantNo)
	if err != nil {
		// 如果发生错误，使用工具方法返回错误响应。
		util.ResError(c, err)
		return
	}
	if merchant.AlipayAppAuthToken != "" {
		wechat, _ := lo.Find(types, func(p *model.PaymentTypeModel) bool {
			return p.ID == consts.PAY_TYPE_WECHAT
		})
		if wechat != nil {
			wechat.NameZh = "二维码支付"
			wechat.NameUg = "ئىككىلىك كود"
			wechat.Icon = "/uploads/images/payment_type/qrcode.png"
		}
	}
	// 过滤掉支付宝
	types = lo.Filter(types, func(item *model.PaymentTypeModel, i int) bool {
		return item.ID != consts.PAY_TYPE_ALIPAY && item.State == model.PaymentTypeStateNormal
	})
	payTypeResource := resource.PaymentTypeResource{}
	tableDetails.PayType = payTypeResource.Collection(types)

	// 返回成功响应，包含订单详细信息。
	util.ResJSON(c, 200, tableDetails)
}

// 订单详情
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 订单详情
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/order/:order_id [get]
// @Param order_id path int64 true "订单ID"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/2 12:40"]
// @X-Version ["2.0"]
func (ctrl *OrderController) OrderDetails(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")

	// 从请求URL参数中获取订单ID，并转换为整数类型
	orderId := util.StrToInt64(c.Param("order_id"))
	// 调用业务逻辑层方法获取订单详情
	order, err := ctrl.OrderCloudService.OrderDetails(ctx, merchantNo, orderId)
	if err != nil {
		// 如果获取订单详情失败，返回错误响应给客户端
		util.ResError(c, err)
		return
	}
	// 如果获取订单详情成功，返回订单信息给客户端
	util.ResJSON(c, 200, order)
}

// PaymentList 根据ID获取订单支付列表。
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 订单支付列表
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/payment-list/:id [get]
// @Param id path int64 true "订单ID"
// @X-Author ["merdan"]
// @X-Date ["2025/4/2 10:39"]
// @X-Version ["2.0"]
func (ctrl *OrderController) PaymentList(c *gin.Context) {
	ctx := c.Request.Context()
	// 从请求参数中提取订单ID。
	id := util.StrToInt64(c.Param("id"))
	merchantNo := c.Request.Header.Get("Merchantno")

	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if order == nil {
		util.ResError(c, errors.BadRequest("", "InvalidOrderID"))
		return
	}

	// 调用业务逻辑层方法，获取指定订单ID的支付列表。
	payments, err := ctrl.PaymentService.ListByOrderNo(ctx, order.No, merchantNo, nil)
	if err != nil {
		// 如果发生错误，使用工具方法返回错误响应。
		util.ResError(c, errors.BadRequest("", "PaymentListFailed"))
		return
	}

	paymentResource := order_resource.OrderPaymentResource{}
	// 返回成功响应，包含支付列表。
	util.ResSuccess(c, paymentResource.Collection(&ctx, payments))
}

// 加菜
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 加菜
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/add-foods/:id [post]
// @Param id path int64 true "订单ID"
// @Param body body request.FoodSellClearDataRequest true "加菜数据"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/9 10:50"]
// @X-Version ["2.0"]
func (ctrl *OrderController) AddFoods(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析URL参数中的订单ID
	orderId := util.StrToInt64(c.Param("id"))

	// 初始化订单加菜请求对象
	orderRequest := order_request.OrderAddFoodRequest{}
	// 绑定HTTP请求体到订单加菜请求对象
	if err := util.ParseJSON(c, &orderRequest); err != nil {
		// 如果绑定失败，返回错误响应
		util.ResError(c, errors.BadRequest("", "FailedToParseJson"))
		return
	}
	// 调用业务逻辑添加菜品到订单
	tableId, list, err := ctrl.OrderCloudService.AddFoods(ctx, merchantNo, userID, userName, orderId, &orderRequest)
	if err != nil {
		// 如果添加失败，返回错误响应
		util.ResError(c, err)
		return
	}

	// 触发打印任务
	ctrl.PrintService.FoodAction(ctx, merchantNo, "加菜", tableId, orderId, userName, list)

	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionAddFood, merchantNo, tableId, orderId, nil)
	// 返回成功响应
	util.ResOK(c)
}

// 退菜接口
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 退菜
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/cancel-food/:order_id/:od_id [post]
// @Param order_id path int64 true "订单ID"
// @Param od_id path int64 true "订单详细ID"
// @Param body body request.CancelFood true "退菜数据"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/9 23:07"]
// @X-Version ["2.0"]
func (ctrl *OrderController) CancelFood(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析订单ID。
	orderId := util.StrToInt64(c.Param("order_id"))
	odID := util.StrToInt64(c.Param("od_id"))

	if odID == 0 || orderId == 0 {
		util.ResError(c, errors.BadRequest("", "FailedToParseQuery"))
		return
	}

	// 初始化取消菜品的请求对象。
	cancelFood := order_request.CancelFood{}
	// 绑定请求体到cancelFood对象。
	if err := util.ParseJSON(c, &cancelFood); err != nil {
		// 如果绑定失败，返回错误响应。
		util.ResError(c, errors.BadRequest("", "FailedToParseJson"))
		return
	}

	// 调用业务逻辑层的取消菜品方法。
	tableId, list, err := ctrl.OrderCloudService.CancelFood(ctx, userID, userName, merchantNo, orderId, odID, &cancelFood)
	if err != nil {
		// 如果取消菜品的过程中出现错误，返回错误响应。
		util.ResError(c, err)
		return
	}
	// 调用打印任务业务逻辑，记录退菜操作。
	ctrl.PrintService.FoodAction(ctx, merchantNo, "退菜", tableId, orderId, userName, list)
	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionCancelFood, merchantNo, tableId, orderId, nil)
	// 返回成功响应。
	util.ResOK(c)
}

// 全单退菜接口
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 全单退菜
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/orders/cancel-all-foods/:order_id [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/17 10:35"]
// @X-Version ["2.0"]
func (ctrl *OrderController) CancelAllFoods(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析订单ID。
	orderId := util.StrToInt64(c.Param("order_id"))

	req := &order_request.CancelAllFoodRequest{}
	if err := util.ParseJSON(c, req); err != nil {
		// 如果绑定失败，返回错误响应。
		util.ResError(c, errors.BadRequest("", "FailedToParseJson"))
		return
	}

	// 调用业务逻辑层的取消所有菜品方法。
	tableId, list, err := ctrl.OrderCloudService.CancelAllFoods(ctx, userID, merchantNo, orderId, req)
	if err != nil {
		// 如果取消菜品的过程中出现错误，返回错误响应。
		util.ResError(c, err)
		return
	}
	// 调用打印任务业务逻辑，记录退菜操作。
	ctrl.PrintService.FoodAction(ctx, merchantNo, "退菜", tableId, orderId, userName, list)

	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionCancelAll, merchantNo, tableId, orderId, list)
	// 返回成功响应。
	util.ResOK(c)
}

func (ctrl *OrderController) UndoCancelFood(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析订单 ID
	orderId := util.StrToInt64(c.Param("order_id"))
	odID := util.StrToInt64(c.Param("od_id"))

	// 调用业务逻辑撤销取消菜品操作
	tableId, list, err := ctrl.OrderCloudService.UndoCancelFood(c, merchantNo, userID, userName, orderId, odID)
	if err != nil {
		// 如果操作失败，返回错误响应
		util.ResError(c, err)
		return
	}

	// 触发加菜打印任务
	ctrl.PrintService.FoodAction(ctx, merchantNo, "加菜", tableId, orderId, userName, list)

	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionAddFood, merchantNo, tableId, orderId, nil)
	// 返回成功响应
	util.ResOK(c)
}

// Checkout 订单结算。
//
// @Tags 订单相关接口
// @Security ServerTokenAuth
// @Summary 订单结算
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/checkout/:id [post]
// @Param id path int64 true "订单ID"
// @X-Author ["Merdan"]
// @X-Date ["2025/4/9 16:16"]
// @X-Version ["2.0"]
func (ctrl OrderController) Checkout(c *gin.Context) {
	ctx := c.Request.Context()

	orderId := util.StrToInt64(c.Param("id"))
	merchantNo := c.Request.Header.Get("Merchantno")
	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, orderId, merchantNo)
	if err != nil {
		// 如果订单不存在，返回错误。
		util.ResError(c, errors.BadRequest("", "OrderNotExist"))
		return
	}
	if order == nil {
		util.ResError(c, errors.BadRequest("", "OrderNotExist"))
		return
	}
	if order.State != 1 && order.State != 2 {
		util.ResError(c, errors.BadRequest("", "OrderStateNotAllowCheckout"))
		return
	}

	userID := util.FromUserID(ctx)

	employee, err := ctrl.EmployeeService.GetByUserID(ctx, merchantNo, userID)

	paymentStatus := consts.PAY_STATUS_PAID
	paymentList, err := ctrl.PaymentService.ListByOrderNo(ctx, order.No, merchantNo, &paymentStatus)
	if err != nil {
		// 如果发生错误，使用工具方法返回错误响应。
		util.ResError(c, errors.BadRequest("", "PaymentListFailed"))
		return
	}
	// 检查是否有有效的支付记录。
	if len(paymentList) == 0 {
		util.ResError(c, errors.BadRequest("", "OrderNotPaid"))
		return
	}

	// 用户付款总额初始化。
	var paymentAmount float64 = 0
	// 是否是VIP支付的标志。
	var isVip bool
	// VIP支付的详细信息。
	var vipPayment model.MerchantPaymentModel

	// 遍历支付列表，确定是否是VIP支付以及计算支付总额。
	for _, item := range paymentList {
		if item.PaymentTypeID == 4 && item.Status == 1 {
			isVip = true
			vipPayment = *item
		} else if item.Status == 1 {
			isVip = false
			continue
		}
	}
	// 如果是VIP支付，验证支付金额是否与VIP折扣后的价格一致。
	if isVip {
		// 计算VIP折扣后的价格。
		paymentAmount = util.DivideFloat(float64(vipPayment.Amount), 100)
		if paymentAmount != (util.SubtractFloat(order.VipPrice, order.IgnorePrice)) {
			util.ResError(c, errors.BadRequest("", "VipPaymentAmountError"))
			return
		}
	} else {
		// 如果不是VIP支付，计算所有有效支付的总额。
		for _, item := range paymentList {
			if item.Status == 1 {
				paymentAmount = util.AddFloat(paymentAmount, float64(item.Amount))
			}
		}
		paymentAmount = util.DivideFloat(paymentAmount, 100)
		// 检查支付总额是否达到订单原价减去忽略价格后的金额。
		if paymentAmount < util.SubtractFloat(order.OriginalPrice, order.IgnorePrice) {
			util.ResError(c, errors.BadRequest("", "PaymentAmountInsufficient"))
			return
		}
	}

	err = ctrl.Trans.Exec(c.Request.Context(), func(ctx context.Context) error {
		// 更新订单的收集金额。
		order.CollectedAmount = paymentAmount
		// 根据是否是VIP支付，更新订单的价格和找零。
		if isVip {
			order.Price = util.SubtractFloat(order.VipPrice, order.IgnorePrice)
			order.GiveChange = 0
		} else {
			order.Price = util.SubtractFloat(order.OriginalPrice, order.IgnorePrice)
			order.GiveChange = util.SubtractFloat(paymentAmount, order.Price)
		}
		now := time.Now()
		// 更新订单的收银员信息和状态。
		IsZh := i18n.IsZh(&ctx)
		order.CashierID = employee.UserID
		order.CashierName = employee.NameUg
		if IsZh {
			order.CashierName = employee.NameZh
		}
		order.State = 3
		order.PaidAt = &now
		// 更新数据库中的订单信息。
		if err = ctrl.OrderCloudService.UpdateOrder(ctx, order); err != nil {
			return errors.BadRequest("", "OrderUpdateFailed")
		}
		// 处理订单详情的付费逻辑。
		if err = ctrl.OrderCloudService.HandlePaid(ctx, order, isVip); err != nil {
			return errors.BadRequest("", "OrderDetailUpdateFailed")
		}
		return nil
	})
	// 如果事务执行失败，返回错误。
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 返回成功响应。
	util.ResOK(c)
	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionCheckout, merchantNo, order.TableID, orderId, nil)
	// 同步订单
	ctrl.OrderSyncHandler.SyncOrder(ctx, orderId, merchantNo)

}

// 催单
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 催单
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/urge/:table_id/:order_id/:order_detail_id [get]
// @Param table_id path int64 true "餐桌ID"
// @Param order_id path int64 true "订单ID"
// @Param order_detail_id path int64 true "订单详细ID"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/10 16:11"]
// @X-Version ["2.0"]
func (ctrl *OrderController) Urge(c *gin.Context) {
	ctx := c.Request.Context()
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	orderID := util.StrToInt64(c.Param("order_id"))
	tableID := util.StrToInt64(c.Param("table_id"))
	odID := util.StrToInt64(c.Param("order_detail_id"))

	// 获取指定订单号的订单详情列表
	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, orderID, merchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "GetFailed"))
		return
	}
	if order.State != 1 && order.State != 2 {
		util.ResError(c, errors.BadRequest("", "OrderStateNotAllowReverse"))
		return
	}

	// 获取指定订单号的订单详情列表
	od, err := ctrl.OrderCloudService.GetCloudOrderDetailByID(ctx, odID, merchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "GetFailed"))
		return
	}

	if od.State != 1 && od.State != 2 {
		util.ResError(c, errors.BadRequest("", "OrderStateNotAllowReverse"))
		return
	}

	// 执行催单操作，如果操作失败，返回错误信息
	ctrl.PrintService.FoodAction(ctx, merchantNo, "催菜", tableID, orderID, userName, append([]*model.OrderDetailModel{}, od))

	if err != nil {
		util.ResError(c, errors.BadRequest("", "OperationFailed"))
		return
	}
	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionUrgeOrder, order.MerchantNo, order.TableID, order.ID, &od)
	// 返回成功响应
	util.ResOK(c)
}

func (ctrl *OrderController) UrgeAll(c *gin.Context) {
	ctx := c.Request.Context()
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	orderID := util.StrToInt64(c.Param("order_id"))
	tableID := util.StrToInt64(c.Param("table_id"))

	// 获取指定订单号的订单详情列表
	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, orderID, merchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "GetFailed"))
		return
	}
	if order.State != 1 && order.State != 2 {
		util.ResError(c, errors.BadRequest("", "OrderStateNotAllowReverse"))
		return
	}

	// 获取订单详情
	list, err := ctrl.OrderCloudService.GetCloudOrderDetailByOrderNo(ctx, order.No, []int{1, 2}, merchantNo)
	if err != nil {
		// 如果获取失败，返回错误响应
		util.ResError(c, errors.BadRequest("", "GetFailed"))
		return
	}
	// 检查订单中是否有未完成的菜品
	if len(list) == 0 {
		// 如果没有，返回错误响应
		util.ResError(c, errors.BadRequest("", "FoodNotInOrder"))
		return
	}

	// 执行催菜操作
	ctrl.PrintService.FoodAction(ctx, merchantNo, "催菜", tableID, orderID, userName, list)

	// 推送广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionUrgeOrder, order.MerchantNo, order.TableID, order.ID, list)
	// 返回成功响应
	util.ResOK(c)
}

// 拆分订单
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 拆分订单
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/orders/split/:order_id [get]
// @Param order_id path int64 true "订单ID"
// @Param body body order_request.SplitOrder true "拆弹数据"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/15 11:50"]
// @X-Version ["2.0"]
func (ctrl *OrderController) SplitOrder(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析订单ID参数
	orderID := util.StrToInt64(c.Param("order_id"))
	// 初始化分割订单请求对象，并从请求体中绑定数据
	splitOrderReq := &order_request.SplitOrder{}
	err := util.ParseJSON(c, splitOrderReq)
	// 验证请求体解析和用户令牌获取是否成功
	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToParseQuery", orderID))
		return
	}

	// 调用业务逻辑进行订单分割
	order, err := ctrl.OrderCloudService.SplitOrder(ctx, orderID, userID, userName, merchantNo, splitOrderReq)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "OrderSplitError"))
		return
	}

	// 通过广播系统发送订单更新通知
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionAddOrder, merchantNo, order.TableID, order.ID, nil)
	// 返回成功响应
	util.ResOK(c)
}

// 并单
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 并单
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/collage/:order_id [post]
// @Param order_id path int64 true "被合并订单ID"
// @Param body body order_request.CollageOrderRequest true "目标订单ID"
// @X-Author ["Habibulla"]
// @X-Date ["2025/4/17 10:36"]
// @X-Version ["2.0"]
func (ctrl *OrderController) CollageOrder(c *gin.Context) {
	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	userName := util.FromUserName(ctx)
	merchantNo := c.Request.Header.Get("Merchantno")

	// 从URL参数中获取订单ID并将其转换为整数。
	orderID := util.StrToInt64(c.Param("order_id"))

	// 初始化一个结构体以解析请求体中的目标订单ID。
	toid := &order_request.CollageOrderRequest{}

	// 解析请求体以获取目标订单ID。
	err := util.ParseJSON(c, toid)
	if err != nil {
		// 如果解析失败，返回错误响应。
		util.ResError(c, errors.BadRequest("", "FailedToParseQuery", orderID))
		return
	}

	if toid.TargetOrderId == orderID {
		// 如果目标订单ID与当前订单ID相同，返回错误响应。
		util.ResError(c, errors.BadRequest("", "TargetOrderSameAsCurrent"))
		return
	}

	// 执行并单操作。
	table, targetTable, order, targetOrder, err := ctrl.OrderCloudService.CollageOrder(ctx, userID, userName, orderID, toid.TargetOrderId, merchantNo)
	if err != nil {
		// 如果并单失败，返回错误响应。
		util.ResError(c, errors.BadRequest("", "OrderMergeError"))
		return
	}

	// 执行与并单相关的打印任务。
	ctrl.PrintService.TableAction(ctx, merchantNo, "并单", table, targetTable, order, targetOrder, userName)

	// 推送并单广播。
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionCollageTable, merchantNo, order.TableID, order.ID, map[string]interface{}{
		"table_id":             table.ID,
		"table_name_zh":        table.NameZh,
		"table_name_ug":        table.NameUg,
		"target_table_id":      targetTable.ID,
		"target_table_name_zh": targetTable.NameZh,
		"target_table_name_ug": targetTable.NameUg,
		"order_id":             order.ID,
		"order_no":             order.No,
		"target_order_id":      targetOrder.ID,
		"target_order_no":      targetOrder.No,
		"operator":             userName,
	})

	// 返回成功响应。
	util.ResOK(c)
}

// IgnorePrice 抹零
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 订单相关接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/ [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/11 16:25"]
// @X-Version ["2.0"]
func (ctrl *OrderController) IgnorePrice(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")

	orderID := util.StrToInt64(c.Query("order_id"))
	ignorePrice, err := strconv.ParseFloat(c.Query("price"), 64)
	if err != nil {
		// 如果转换失败，返回错误的价格请求。
		util.ResError(c, errors.BadRequest("", "InvalidPrice"))
		return
	}

	// 获取订单
	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, orderID, merchantNo)
	if err != nil || order == nil {
		// 如果获取订单失败，返回错误响应。
		util.ResError(c, errors.BadRequest("", "OrderNotFound"))
		return
	}

	if order.State != 1 && order.State != 2 {
		// 如果订单状态不允许忽略价格，返回错误响应。
		util.ResError(c, errors.BadRequest("", "OrderStateNotAllowPay"))
		return
	}

	// 更新订单的价格和找零。
	err = ctrl.OrderCloudService.UpdateIgnorePrice(ctx, orderID, merchantNo, ignorePrice)
	if err != nil {
		// 如果更新失败，返回错误响应。
		util.ResError(c, errors.BadRequest("", "OperateFailed"))
		return
	}
	// 返回成功响应。
	util.ResOK(c)

}

// 换桌
//
// @Tags 订单相关接口
// @Security ApiTokenAuth
// @Summary 订单相关接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/orders/changeTable/:order_id [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/16 19:45"]
// @X-Version ["2.0"]
func (ctrl *OrderController) ChangeTable(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")

	// 解析订单ID参数
	orderID := util.StrToInt64(c.Param("order_id"))
	// 初始化分割订单请求对象，并从请求体中绑定数据
	changeTableReq := &order_request.ChangeTableRequest{}
	err := util.ParseJSON(c, changeTableReq)
	// 验证请求体解析和用户令牌获取是否成功
	if err != nil {
		util.ResError(c, errors.BadRequest("", "FailedToParseQuery", orderID))
		return
	}

	order, err := ctrl.OrderCloudService.GetCloudOrderByID(ctx, orderID, merchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "GetFailed"))
		return
	}
	if order == nil {
		util.ResError(c, errors.BadRequest("", "OrderNotFound"))
		return
	}

	// 检查订单状态，只有状态为1或2的订单可以进行餐桌变更
	if order.State != 1 && order.State != 2 {
		util.ResError(c, errors.BadRequest("", "OrderStateNotAllowChangeTable"))
		return
	}

	// 检查目标餐桌是否与当前订单的餐桌相同，如果相同则不允许变更
	if order.TableID == changeTableReq.TableID {
		util.ResError(c, errors.BadRequest("", "TableNotChanged"))
		return
	}

	// 当前桌子信息
	table, err := ctrl.TableService.GetTableByID(ctx, merchantNo, order.TableID)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "CurrentTableNotFound"))
		return
	}

	if table == nil {
		util.ResError(c, errors.BadRequest("", "CurrentTableNotFound"))
		return
	}

	targetTable, err := ctrl.TableService.GetTableByID(ctx, merchantNo, changeTableReq.TableID)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "TargetTableNotFound"))
		return
	}

	if targetTable == nil {
		util.ResError(c, errors.BadRequest("", "TargetTableNotFound"))
		return
	}

	// 调用业务逻辑进行订单换桌
	err = ctrl.OrderCloudService.ChangeTable(ctx, orderID, merchantNo, changeTableReq.TableID)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "ChangeTableFailed"))
		return
	}

	employee, err := ctrl.EmployeeService.GetByUserID(ctx, merchantNo, util.FromUserID(ctx))
	if err != nil {
		util.ResError(c, errors.BadRequest("", "EmployeeNotFound"))
		return
	}
	if employee == nil {
		util.ResError(c, errors.BadRequest("", "EmployeeNotFound"))
		return
	}

	isZh := i18n.IsZh(&ctx)
	operator := employee.NameUg
	if isZh {
		operator = employee.NameZh
	}
	ctrl.PrintService.TableAction(ctx, merchantNo, "换台", table, targetTable, order, nil, operator)

	// 推送换桌广播
	order_broadcast.SendOrderBroadcast(order_broadcast.ActionChangeTable, merchantNo, table.ID, order.ID, map[string]interface{}{
		"table_id":             table.ID,
		"table_name_zh":        table.NameZh,
		"table_name_ug":        table.NameUg,
		"target_table_id":      targetTable.ID,
		"target_table_name_zh": targetTable.NameZh,
		"target_table_name_ug": targetTable.NameUg,
		"order_id":             order.ID,
		"order_no":             order.No,
		"operator":             operator,
	})

	// 返回成功响应
	util.ResOK(c)
}
