package sync

import (
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/http/resource/sync_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

// FoodsController 菜品同步接口
type FoodsController struct {
	FoodSyncService *food_service.FoodSyncService
}

// GetMerchantFoodsForSync 同步菜品接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品接口
// @Success 200 {object} util.ResponseResult{data=resource.FoodResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:04"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodsForSync(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantFoodsForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	foodResource := food_resource.FoodResource{}
	util.ResSuccess(c, foodResource.Collection(&ctx, result))
}

// GetMerchantFoodCategories 获取商户的食品分类列表
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 获取商户的食品分类列表
// @Success 200 {object} util.ResponseResult{data=[]food_resource.FoodCategoryResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/food-categories [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/3 16:19"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodCategories(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantFoodCategoriesForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, (&food_resource.FoodCategoryResource{}).Collection(&ctx, result))
}

// GetMerchantFoodCombos 同步菜品套餐接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品套餐接口
// @Success 200 {object} util.ResponseResult{data=[]resource.FoodComboResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/food-combos [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:21"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodCombos(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantFoodCombosForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := food_resource.FoodComboResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}

// GetMerchantMethodGroups 同步菜品做法分组接口
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品做法分组接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/15 10:33"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantMethodGroups(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantMethodGroupsForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := food_resource.MethodGroupResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}

// GetMerchantMethods 同步菜品做法接口
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品做法接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/methods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/15 10:33"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantMethods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantMethodsForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := food_resource.MethodResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}

// GetMerchantFoodMethod 同步菜品做法关联接口
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品做法关联接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-method [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/15 10:41"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodMethod(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantFoodMethodForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := sync_resource.FoodMethodResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}

// GetMerchantFoodAddition 同步菜品加料关联接口
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品做法关联接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/15 10:41"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodAddition(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantFoodAdditionForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := sync_resource.FoodAdditionFoodsResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}

// GetMerchantFoodLunchBox 同步菜品餐盒关联接口
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品做法关联接口
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-methods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/15 10:41"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) GetMerchantFoodLunchBox(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodSyncService.GetMerchantFoodLunchBoxForSync(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := sync_resource.FoodLunchBoxResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}

// UpdateFoodSellClearData 同步菜品沽清数据接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品沽清数据接口
// @Param body request.FoodSellClearDataRequest true "沽清数据"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/foods/sell-clear-data [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/2/19 15:38"]
// @X-Version ["2.0"]
func (ctrl *FoodsController) UpdateFoodSellClearData(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem food_request.FoodSellClearDataRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	formItem.MerchantNo = util.FromServerMerchantNo(ctx)

	if err := ctrl.FoodSyncService.UpdateFoodSellClearData(ctx, formItem); err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
}
