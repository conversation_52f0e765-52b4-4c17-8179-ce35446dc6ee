package v2

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/http/resource/food_printer_resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

type PrinterController struct {
	PrinterService            *service.PrinterService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetList 同步餐厅打印机接口
//
// @Tags 同步接口
// @Security ApiTokenAuth
// @Summary 同步餐厅打印机接口
// @Success 200 {object} util.ResponseResult{data=[]resource.PrinterResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/printers [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) GetList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")

	result, err := ctrl.PrinterService.GetMerchantAvailablePrinters(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	printerResource := resource.PrinterResource{}
	util.ResSuccess(c, printerResource.Collection(result))
}

// Create 创建打印机
// @Summary 创建打印机
// @Description 创建打印机
// @Tags 打印机管理
// @Accept json
// @Produce json
// @Param data body request.PrinterCreateRequest true "打印机信息"
// @Success 200 {object} util.ResponseResult{data=resource.PrinterResource}
// @Router /api/v2/local/printers [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) Create(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")

	var req request.PrinterCreateRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}
	req.MerchantNo = merchantNo

	printer, err := ctrl.PrinterService.Create(ctx, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	printerResource := resource.PrinterResource{}
	util.ResSuccess(c, printerResource.Make(printer))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.PrinterUpdated)
}

// Update 更新打印机
// @Summary 更新打印机
// @Description 更新打印机
// @Tags 打印机管理
// @Accept json
// @Produce json
// @Param id path string true "打印机ID"
// @Param data body request.PrinterUpdateRequest true "打印机信息"
// @Success 200 {object} util.ResponseResult{data=resource.PrinterResource}
// @Router /api/v2/local/printers/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) Update(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")

	var req request.PrinterUpdateRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}
	req.MerchantNo = merchantNo
	req.ID = c.Param("id")

	printer, err := ctrl.PrinterService.Update(ctx, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	printerResource := resource.PrinterResource{}
	util.ResSuccess(c, printerResource.Make(printer))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.PrinterUpdated)
}

// Delete 删除打印机
// @Summary 删除打印机
// @Description 删除打印机
// @Tags 打印机管理
// @Produce json
// @Param id path string true "打印机ID"
// @Success 200 {object} util.ResponseResult
// @Router /api/v2/local/printers/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) Delete(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")
	printerID := c.Param("id")

	err := ctrl.PrinterService.Delete(ctx, merchantNo, printerID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.PrinterUpdated)
}

// UpdateStatus 更新打印机状态
// @Summary 更新打印机状态
// @Description 更新打印机状态
// @Tags 打印机管理
// @Accept json
// @Produce json
// @Param id path string true "打印机ID"
// @Param data body request.PrinterStatusRequest true "状态信息"
// @Success 200 {object} util.ResponseResult
// @Router /api/v2/local/printers/{id}/status [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) UpdateStatus(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")
	printerID := c.Param("id")

	var req request.PrinterStatusRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.PrinterService.UpdateStatus(ctx, merchantNo, printerID, req.Status)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.PrinterUpdated)
}

// Get 获取打印机详情
// @Summary 获取打印机详情
// @Description 获取打印机详情
// @Tags 打印机管理
// @Produce json
// @Param id path string true "打印机ID"
// @Success 200 {object} util.ResponseResult{data=resource.PrinterResource}
// @Router /api/v2/local/printers/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) Get(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")
	printerID := c.Param("id")

	printer, err := ctrl.PrinterService.GetByID(ctx, merchantNo, printerID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	printerResource := resource.PrinterResource{}
	util.ResSuccess(c, printerResource.Make(printer))
}

// GetFoodPrinters 获取打印机与美食的关联关系
// @Summary 获取打印机与美食的关联关系
// @Description 获取打印机与美食的关联关系
// @Tags 打印机管理
// @Produce json
// @Success 200 {object} util.ResponseResult{data=[]food_printer_resource.CategoryResource}
// @Router /api/v2/local/printers/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) GetFoodPrinters(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")

	list, err := ctrl.PrinterService.GetFoodPrinters(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	foodCategoryResource := food_printer_resource.CategoryResource{}
	util.ResSuccess(c, foodCategoryResource.Collection(list))
}

// SavePrinterFoods 保存打印机与美食的关联关系
// @Summary 保存打印机与美食的关联关系
// @Description 保存打印机与美食的关联关系
// @Tags 打印机管理
// @Accept json
// @Produce json
// @Param data body request.PrinterFoodsRequest true "打印机与美食关联关系"
// @Success 200 {object} util.ResponseResult
// @Router /api/v2/local/printers/foods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/04/25 17:20"]
// @X-Version ["2.0"]
func (ctrl *PrinterController) SavePrinterFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("MerchantNo")

	var req request.PrinterFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	if err := ctrl.PrinterService.SavePrinterFoods(ctx, merchantNo, &req); err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodPrinterUpdated)
}
