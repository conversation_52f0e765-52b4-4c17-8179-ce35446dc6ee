package merchant

import (
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http"
	"ros-api-go/internal/http/request/merchant_role_request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"strconv"

	"github.com/gin-gonic/gin"
)

type RoleController struct {
	MerchantRoleService       *service.MerchantRoleService
	MerchantEmployeeService   *service.MerchantEmployeeService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
	Casbinx                   *http.Casbinx
}

// List 获取商户角色列表
//
// @Tags 商户管理
// @Security ApiTokenAuth
// @Summary 角色列表
// @Param Merchantno header string true "商户号"
// @Success 200 {object} util.ResponseResult{data=[]resource.MerchantRoleItemResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/merchant/roles [GET]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/19 12:27"]
// @X-Version ["2.0"]
func (ctrl *RoleController) List(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	stateStr := c.Query("state")
	var state *int8
	if stateStr != "" {
		stateInt, _ := strconv.Atoi(stateStr)
		stateint8 := int8(stateInt)
		state = &stateint8
	}

	result, err := ctrl.MerchantRoleService.GetMerchantRoles(ctx, merchantNo, state)
	if err != nil {
		util.ResError(c, err)
		return
	}
	merchantRoleListItemResource := resource.MerchantRoleItemResource{}
	util.ResSuccess(c, merchantRoleListItemResource.Collection(result), "GetSuccess")
}

// Create 创建角色
//
// @Tags 商户管理
// @Security ApiTokenAuth
// @Summary 创建角色
// @Param body body merchant_role_request.CreateRoleRequest true "创建角色请求"
// @Success 200 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/merchant/roles [POST]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/25 12:27"]
// @X-Version ["2.0"]
func (ctrl *RoleController) Create(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")

	request := merchant_role_request.CreateRoleRequest{}
	if err := util.ParseJSON(c, &request); err != nil {
		util.ResError(c, err)
		return
	}

	ctrl.MerchantRoleService.CreateMerchantRole(
		ctx,
		merchantNo,
		request.Name_ug,
		request.Name_zh,
		request.Permissions,
		request.State)
	util.ResOK(c, "CreateSuccess")
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// Update 更新角色
//
// @Tags 商户管理
// @Security ApiTokenAuth
// @Summary 更新角色
// @Param body body merchant_role_request.UpdateRoleRequest true "更新角色请求"
// @Success 200 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/merchant/roles [PUT]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/26 12:27"]
// @X-Version ["2.0"]
func (ctrl *RoleController) Update(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}
	request := merchant_role_request.UpdateRoleRequest{}
	if err := util.ParseJSON(c, &request); err != nil {
		util.ResError(c, err)
		return
	}

	role, err := ctrl.MerchantRoleService.GetMerchantRole(ctx, merchantNo, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if role == nil {
		util.ResError(c, errors.BadRequest("", "DataNotFound"))
		return
	}
	if role.State == model.MerchantRoleStateDeleted {
		util.ResError(c, errors.BadRequest("", "DataNotFound"))
		return
	}

	err = ctrl.MerchantRoleService.UpdateMerchantRole(
		ctx,
		merchantNo,
		id,
		request.Name_ug,
		request.Name_zh,
		request.Permissions,
		request.State)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResOK(c, "UpdateSuccess")
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// UpdateState 更新角色状态
//
// @Tags 商户管理
// @Security ApiTokenAuth
// @Summary 更新角色状态
// @Param id path string true "角色ID"
// @Param body body merchant_role_request.UpdateRoleStateRequest true "更新角色状态请求"
// @Success 200 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/merchant/roles/{id}/state [PUT]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/26 12:33"]
// @X-Version ["2.0"]
func (ctrl *RoleController) UpdateState(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id := util.StrToInt64(c.Param("id"))

	request := merchant_role_request.UpdateRoleStateRequest{}
	if err := util.ParseJSON(c, &request); err != nil {
		util.ResError(c, err)
		return
	}

	role, err := ctrl.MerchantRoleService.GetMerchantRole(ctx, merchantNo, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if role == nil {
		util.ResError(c, errors.BadRequest("", "DataNotFound"))
		return
	}
	if role.State == model.MerchantRoleStateDeleted {
		util.ResError(c, errors.BadRequest("", "DataNotFound"))
		return
	}

	if request.State == model.MerchantRoleStateEnabled {
		err = ctrl.MerchantRoleService.Enable(ctx, merchantNo, id)
	} else {
		err = ctrl.MerchantRoleService.Disable(ctx, merchantNo, id)
	}
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "UpdateSuccess")
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// Delete 删除角色
//
// @Tags 商户管理
// @Security ApiTokenAuth
// @Summary 删除角色
// @Param id path string true "角色ID"
// @Success 200 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/merchant/roles/:id [DELETE]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/26 12:33"]
// @X-Version ["2.0"]
func (ctrl *RoleController) Delete(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	ctrl.MerchantRoleService.DeleteMerchantRole(ctx, merchantNo, id)
	util.ResOK(c, "DeleteSuccess")
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// Get 获取角色
//
// @Tags 商户管理
// @Security ApiTokenAuth
// @Summary 获取角色
// @Param id path string true "角色ID"
// @Success 200 {object} util.ResponseResult{data=resource.MerchantRoleItemResource}
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/merchant/roles/:id [GET]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024/12/26 12:33"]
// @X-Version ["2.0"]
func (ctrl *RoleController) Get(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}
	role, err := ctrl.MerchantRoleService.GetMerchantRole(ctx, merchantNo, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	merchantRoleResource := resource.MerchantRoleResource{}
	util.ResSuccess(c, merchantRoleResource.Make(role), "GetSuccess")
}
