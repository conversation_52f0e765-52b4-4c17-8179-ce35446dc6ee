package merchant

import (
	"net/http"
	"ros-api-go/internal/handler"
	http2 "ros-api-go/internal/http"
	"ros-api-go/internal/http/request/merchant_employee_request"
	"ros-api-go/internal/http/request/merchant_role_request"
	"ros-api-go/internal/http/resource/employee_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

type EmployeeController struct {
	MerchantEmployeeService   *service.MerchantEmployeeService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
	UserService               *service.UserService
	Casbinx                   *http2.Casbinx
}

// List 获取商户员工列表
// @Summary 获取商户员工列表
// @Tags 商户员工
// @Security ApiTokenAuth
// @Accept json
// @Produce json
// @Param page query int true "页码"
// @Param state query int false "状态"
// @Param mobile query string false "手机号"
// @Param Merchantno header string true "商户号"
// @Success 200 {object} employee_resource.EmployeeListResource "商户员工列表"
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /v2/merchant/employees [get]
// @X-Author ["Alim Kirem"]
// @X-Date ["2024-12-31"]
// @X-Version ["2.0"]
func (ctrl *EmployeeController) List(c *gin.Context) {
	merchantNo := c.Request.Header.Get("Merchantno")
	page, _ := strconv.Atoi(c.Query("page"))
	stateStr, _ := c.GetQuery("state")
	mobile, _ := c.GetQuery("mobile")
	var state *int8
	if stateStr != "" {
		stateInt, _ := strconv.Atoi(stateStr)
		stateint8 := int8(stateInt)
		state = &stateint8
	}
	var pageSize int = 10

	items, total, err := ctrl.MerchantEmployeeService.List(c.Request.Context(), merchantNo, state, mobile, page, pageSize)
	if err != nil {
		util.ResError(c, err)
		return
	}
	c.JSON(http.StatusOK, employee_resource.NewEmployeeListResource(items, total, page, pageSize))
}

// Create 创建商户员工
// @Summary 创建商户员工
// @Description 创建商户员工
// @Tags 商户员工
// @Accept json
// @Produce json
// @Param merchant_employee body merchant_employee_request.CreateEmployeeRequest true "商户员工信息"
// @Success 200 {object} employee_resource.ListItemResource "商户员工信息"
// @Router /v2/merchant/employees [post]
// @X-Author ["Alim Kirem"]
// @X-Date ["2025-01-02"]
// @X-Version ["2.0"]
func (ctrl *EmployeeController) Create(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	var request merchant_employee_request.CreateEmployeeRequest
	if err := util.ParseJSON(c, &request); err != nil {
		util.ResError(c, err)
		return
	}

	user, err := ctrl.UserService.FindOrCreate(c.Request.Context(), request.Phone)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 检查用户是否存在
	if employee, err := ctrl.MerchantEmployeeService.GetByUserID(c.Request.Context(), merchantNo, user.ID); err != nil {
		util.ResError(c, err)
		return
	} else if employee != nil {
		util.ResError(c, errors.BadRequest("", "UserExists"))
		return
	}

	if employee, err := ctrl.MerchantEmployeeService.GetMerchantEmployeeByNo(c.Request.Context(), merchantNo, request.No); err != nil {
		logging.Context(ctx).Error("Check employee no exists failed", zap.Error(err))
		util.ResError(c, err)
		return
	} else if employee != nil {
		logging.Context(ctx).Error("Employee no exists", zap.String("employee_no", request.No))
		util.ResError(c, errors.ValidationError("", "EmployeeNoExists"))
		return
	}

	employeeData := model.MerchantEmployeeModel{}
	employeeData.No = request.No
	employeeData.MerchantNo = merchantNo
	employeeData.NameUg = request.NameUg
	employeeData.NameZh = request.NameZh
	employeeData.UserID = user.ID
	employeeData.State = request.State
	employeeData.IsOwner = model.MerchantEmployeeNotOwner

	_, err = ctrl.MerchantEmployeeService.Create(
		c.Request.Context(),
		employeeData,
		request.RoleIds)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// Update 更新商家员工信息
// @Summary 更新商家员工信息
// @Description 更新商家员工信息
// @Tags 商户员工
// @Accept json
// @Produce json
// @Param id path int true "员工ID"
// @Param merchant_employee body merchant_employee_request.UpdateEmployeeRequest true "商户员工信息"
// @Success 200 {object} employee_resource.ListItemResource "商户员工信息"
// @Router /v2/merchant/employees/{id} [put]
// @X-Author ["Alim Kirem"]
// @X-Date ["2025-01-02"]
// @X-Version ["2.0"]
func (ctrl *EmployeeController) Update(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id := util.StrToInt64(c.Param("id"))
	var request merchant_employee_request.UpdateEmployeeRequest
	if err := util.ParseJSON(c, &request); err != nil {
		util.ResError(c, err)
		return
	}
	employee, err := ctrl.MerchantEmployeeService.Get(ctx, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if employee.IsOwner {
		util.ResError(c, errors.Forbidden("", "OwnerCannotBeEdit"))
		return
	}
	if employee.State == model.EmployeeStateDeleted ||
		employee.MerchantNo != merchantNo {
		util.ResError(c, errors.NotFound("EmployeeNotFound", "EmployeeNotFound"))
		return
	}
	if request.No != employee.No {
		if employeeByNo, err := ctrl.MerchantEmployeeService.GetMerchantEmployeeByNo(ctx, merchantNo, request.No); err != nil {
			util.ResError(c, err)
			return
		} else if employeeByNo != nil {
			util.ResError(c, errors.ValidationError("", "EmployeeNoExists"))
			return
		}
	}
	employee.NameUg = request.NameUg
	employee.NameZh = request.NameZh
	employee.No = request.No
	employee.State = request.State
	employee, err = ctrl.MerchantEmployeeService.UpdateWithRoleIds(ctx, *employee, request.RoleIds)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResOK(c)
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

func (ctrl *EmployeeController) UpdateState(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id := util.StrToInt64(c.Param("id"))

	request := merchant_role_request.UpdateRoleStateRequest{}
	if err := util.ParseJSON(c, &request); err != nil {
		util.ResError(c, err)
		return
	}

	employee, err := ctrl.MerchantEmployeeService.Get(ctx, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if employee.IsOwner {
		util.ResError(c, errors.Forbidden("", "OwnerCannotBeEdit"))
		return
	}
	if employee.State == model.EmployeeStateDeleted {
		util.ResError(c, errors.BadRequest("", "UserNotFound"))
		return
	}

	if request.State == model.EmployeeStateActive {
		err = ctrl.MerchantEmployeeService.Enable(ctx, merchantNo, id)
	} else {
		err = ctrl.MerchantEmployeeService.Disable(ctx, merchantNo, id)
	}
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "UpdateSuccess")
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// Delete 删除商户员工
// @Summary 删除商户员工
// @Description 删除商户员工
// @Tags 商户员工
// @Accept json
// @Produce json
// @Param id path int true "员工ID"
// @Router /v2/merchant/employees/{id} [delete]
// @X-Author ["Alim Kirem"]
// @X-Date ["2025-01-02"]
// @X-Version ["2.0"]
func (ctrl *EmployeeController) Delete(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.NotFound("EmployeeNotFound", "EmployeeNotFound"))
		return
	}
	employee, err := ctrl.MerchantEmployeeService.Get(ctx, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if employee.IsOwner {
		util.ResError(c, errors.Forbidden("", "OwnerCannotBeEdit"))
		return
	}
	if employee.MerchantNo != merchantNo || employee.State == model.EmployeeStateDeleted {
		util.ResError(c, errors.NotFound("EmployeeNotFound", "EmployeeNotFound"))
		return
	}
	err = ctrl.MerchantEmployeeService.Delete(ctx, *employee)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, nil)
	ctrl.Casbinx.ClearMerchantCasbinCache(merchantNo)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.UserUpdated)
}

// Get 获取员工信息
// @Summary 获取员工信息
// @Description 获取员工信息
// @Tags 商户员工
// @Accept json
// @Produce json
// @Param id path int true "员工ID"
// @Success 200 {object} employee_resource.ListItemResource "商户员工信息"
// @Router /v2/merchant/employees/{id} [get]
// @X-Author ["Alim Kirem"]
// @X-Date ["2025-01-02"]
// @X-Version ["2.0"]
func (ctrl *EmployeeController) Get(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := c.Request.Header.Get("Merchantno")
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.NotFound("EmployeeNotFound", "EmployeeNotFound"))
		return
	}
	employee, err := ctrl.MerchantEmployeeService.GetWithRolesAndUser(ctx, id)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if employee.MerchantNo != merchantNo || employee.State == model.EmployeeStateDeleted {
		util.ResError(c, errors.NotFound("EmployeeNotFound", "EmployeeNotFound"))
		return
	}

	c.JSON(http.StatusOK, employee_resource.NewEmployeeDetailResource(*employee))
}
