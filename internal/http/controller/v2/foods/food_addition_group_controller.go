package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type FoodAdditionGroupController struct {
	FoodAdditionGroupService  food_service.FoodAdditionGroupService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetFoodAdditionGroupList 获取加料分组列表（分页）
//
// @Tags 加料分组
// @Security ApiAuthToken
// @Summary 获取加料分组列表（分页）
// @Success 200 {object} util.ResponseResult{Data=[]food_resource.FoodAdditionGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition-groups [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:54"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionGroupController) GetFoodAdditionGroupList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.FoodAdditionGroupListRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	req.MerchantNo = merchantNo
	result, err := ctrl.FoodAdditionGroupService.GetList(ctx, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodAdditionGroupResource{}).Collection(&ctx, result))
}

// GetFoodAdditionGroups 获取单个加料分组
//
// @Tags 加料分组
// @Security ApiAuthToken
// @Summary 获取单个加料分组
// @Success 200 {object} util.ResponseResult{ Data=food_resource.FoodAdditionGroupResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition-groups/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionGroupController) GetFoodAdditionGroups(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	result, err := ctrl.FoodAdditionGroupService.GetByID(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodAdditionGroupResource{}).Make(&ctx, result))
}

// CreateFoodAdditionGroup 创建加料分组
//
// @Tags 加料分组
// @Security ApiAuthToken
// @Summary  创建加料分组
// @Param request body food_request.CreateFoodAdditionGroupRequest true "创建加料分组请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=food_resource.FoodAdditionGroupResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition-groups [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionGroupController) CreateFoodAdditionGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.CreateFoodAdditionGroupRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.FoodAdditionGroupService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// UpdateFoodAdditionGroup 更新加料分组
//
// @Tags 加料分组
// @Security ApiAuthToken
// @Summary 更新加料分组
// @Param id path int true "加料分组ID"
// @Param request body food_request.UpdateFoodAdditionGroupRequest true "更新加料分组请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=food_resource.FoodAdditionGroupResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition-groups/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionGroupController) UpdateFoodAdditionGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.UpdateFoodAdditionGroupRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.FoodAdditionGroupService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// DeleteFoodAdditionGroup 删除加料分组
//
// @Tags 加料分组
// @Security ApiAuthToken
// @Summary 删除加料分组
// @Param id path int true "加料分组ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition-groups/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionGroupController) DeleteFoodAdditionGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.FoodAdditionGroupService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// SaveFoodAdditionGroupSort 保存美食分类排序
//
// @Tags 加料分组
// @Security ApiAuthToken
// @Summary 保存美食分类排序
// @Param request body food_request.SaveFoodAdditionGroupSortRequest true "排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-addition-groups/sort [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 17:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionGroupController) SaveFoodAdditionGroupSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveFoodAdditionGroupSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.FoodAdditionGroupService.SaveSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "SortSaveSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}
