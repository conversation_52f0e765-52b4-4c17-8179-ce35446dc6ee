package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type FoodSpecController struct {
	FoodSpecService           food_service.FoodSpecService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetFoodSpecList 获取规格列表
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 获取规格列表
// @Param search query string false "搜索关键词（支持中文和维语名称搜索）"
// @Success 200 {object} util.ResponseResult{Data=[]food_resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) GetFoodSpecList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.FoodSpecListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodSpecService.GetList(ctx, merchantNo, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodSpecResource{}).Collection(result))
}

// GetFoodSpec 获取单个规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 获取单个规格
// @Success 200 {object} util.ResponseResult{Data=food_resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) GetFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	result, err := ctrl.FoodSpecService.GetByID(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodSpecResource{}).Make(result))
}

// CreateFoodSpec 创建规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 创建规格
// @Param request body food_request.CreateFoodSpecRequest true "创建规格请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) CreateFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.CreateFoodSpecRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodSpecService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodSpecResource{}).Make(result))
}

// UpdateFoodSpec 更新规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 更新规格
// @Param id path int true "规格ID"
// @Param request body food_request.UpdateFoodSpecRequest true "更新规格请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.FoodSpecResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) UpdateFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.UpdateFoodSpecRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodSpecService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodSpecResource{}).Make(result))
}

// DeleteFoodSpec 删除规格
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 删除规格
// @Param id path int true "规格ID"
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) DeleteFoodSpec(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.FoodSpecService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, nil)
}

// GetSpecFoods 获取规格关联的美食列表
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 获取规格关联的美食列表
// @Param id path int true "规格ID"
// @Success 200 {object} util.ResponseResult{Data=[]food_resource.FoodSpecFoodsResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/{id}/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 16:30"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) GetSpecFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	specID := util.StrToInt64(c.Param("id"))

	if specID == 0 {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	foods, err := ctrl.FoodSpecService.GetSpecFoods(ctx, specID, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodSpecFoodsResource{}).Collection(foods))
}

// SaveFoodSpecFoods 保存规格美食关联关系
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 保存规格美食关联关系
// @Param request body food_request.SaveFoodSpecFoodsRequest true "保存规格美食关联关系请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/foods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) SaveFoodSpecFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveFoodSpecFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.FoodSpecService.SaveFoodSpecFoods(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, nil)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodRelationUpdated)
}

// RemoveFoodSpecFoods 解除规格美食关联关系
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 解除规格美食关联关系
// @Param request body food_request.RemoveFoodSpecFoodsRequest true "解除关联请求"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 400 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/foods/remove [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 16:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) RemoveFoodSpecFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.RemoveFoodSpecFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidRequestFormat"))
		return
	}

	err := ctrl.FoodSpecService.RemoveFoodSpecFoods(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodRelationUpdated)
}

// SaveFoodSpecSort 保存规格排序
//
// @Tags 规格管理
// @Security ApiAuthToken
// @Summary 保存规格排序
// @Param request body food_request.SaveFoodSpecSortRequest true "排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-specs/sort [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/04 17:00"]
// @X-Version ["2.0"]
func (ctrl *FoodSpecController) SaveFoodSpecSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveFoodSpecSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.FoodSpecService.SaveSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "SortSaveSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}
