package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type FoodAdditionController struct {
	FoodAdditionService       food_service.FoodAdditionService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetFoodAdditionList 获取加料列表
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 获取加料列表
// @Produce json
// @Param page query int false "页码"
// @Param limit query int false "每页数量"
// @Param category_id query int false "加料分类ID"
// @Param keyword query string false "搜索关键词"
// @Param state query int false "状态"
// @Success 200 {object} util.ResponseResult{Data=util.PaginationResult{data=[]food_resource.FoodAdditionResource}}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) GetFoodAdditionList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.FoodAdditionListRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	list, err := ctrl.FoodAdditionService.GetFoodAdditionList(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodAdditionResource{}).Collection(list))
}

// CreateFoodAddition 创建加料
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 创建加料
// @Param request body food_request.CreateFoodAdditionRequest true "创建加料请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.FoodAdditionResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) CreateFoodAddition(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.CreateFoodAdditionRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodAdditionService.CreateFoodAddition(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodAdditionResource{}).Make(result))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// UpdateFoodAddition 更新加料
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 更新加料
// @Param id path int true "加料ID"
// @Param request body food_request.UpdateFoodAdditionRequest true "更新加料请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.FoodAdditionResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) UpdateFoodAddition(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.UpdateFoodAdditionRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodAdditionService.UpdateFoodAddition(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodAdditionResource{}).Make(result))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// DeleteFoodAddition 删除加料
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 删除加料
// @Param id path int true "加料ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) DeleteFoodAddition(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.FoodAdditionService.DeleteFoodAddition(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// GetAdditionFoods 获取加料关联的美食列表
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 获取加料关联的美食列表
// @Param id path int true "加料ID"
// @Param page query int false "页码"
// @Param limit query int false "每页数量"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=util.PaginationResult{data=[]food_resource.FoodAdditionFoodsResource}}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions/{id}/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) GetAdditionFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.GetFoodAdditionFoodsRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, list, err := ctrl.FoodAdditionService.GetAdditionFoods(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodAdditionFoodsResource{}).Collection(list))
}

// SaveFoodAdditionFoods 关联菜品
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 关联菜品
// @Param request body food_request.SaveFoodAdditionFoodsRequest true "关联菜品请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions/foods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) SaveFoodAdditionFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveFoodAdditionFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.FoodAdditionService.SaveFoodAdditionFoods(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodRelationUpdated)
}

// RemoveFoodAdditionFoods 解除关联
//
// @Tags 加料管理
// @Security ApiAuthToken
// @Summary 解除关联
// @Param request body food_request.RemoveFoodAdditionFoodsRequest true "解除关联请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/food-additions/foods [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodAdditionController) RemoveFoodAdditionFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.RemoveFoodAdditionFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.FoodAdditionService.RemoveFoodAdditionFoods(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodRelationUpdated)
}
