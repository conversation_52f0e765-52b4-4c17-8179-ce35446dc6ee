package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type MethodGroupController struct {
	MethodGroupService        *food_service.MethodGroupService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetMethodGroupList 获取做法分组列表
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 获取做法分组列表
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} util.ResponseResult{Data=[]food_resource.MethodGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) GetMethodGroupList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.MethodGroupListRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	methodGroups, err := ctrl.MethodGroupService.GetList(ctx, merchantNo, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.MethodGroupResource{}).Collection(methodGroups))
}

// CreateMethodGroup 创建做法分组
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 创建做法分组
// @Param request body food_request.CreateMethodGroupRequest true "创建做法分组请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.MethodGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) CreateMethodGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.CreateMethodGroupRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.MethodGroupService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// UpdateMethodGroup 更新做法分组
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 更新做法分组
// @Param id path int true "做法分组ID"
// @Param request body food_request.UpdateMethodGroupRequest true "更新做法分组请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.MethodGroupResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) UpdateMethodGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.UpdateMethodGroupRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.MethodGroupService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// DeleteMethodGroup 删除做法分组
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 删除做法分组
// @Param id path int true "做法分组ID"
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) DeleteMethodGroup(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.MethodGroupService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "DeleteSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}

// SaveMethodGroupSort 保存做法分组排序
//
// @Tags 做法分组管理
// @Security ApiAuthToken
// @Summary 保存做法分组排序
// @Param request body food_request.SaveMethodGroupSortRequest true "排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/method-groups/sort [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/07 18:00"]
// @X-Version ["2.0"]
func (ctrl *MethodGroupController) SaveMethodGroupSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveMethodGroupSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.MethodGroupService.SaveSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c, "SortSaveSuccess")
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.MethodUpdated)
}
