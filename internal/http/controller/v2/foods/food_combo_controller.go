package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type FoodComboController struct {
	FoodComboService          *food_service.FoodComboService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// CreateComboFood 创建美食
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 创建美食
// @Param request body food_request.FoodComboRequest true "创建美食请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=food_resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/combo [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodComboController) CreateComboFood(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.FoodComboRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	// 图片路径处理
	req.Image = util.GetImagePath(req.Image)

	_, err := ctrl.FoodComboService.CreateComboFood(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// UpdateComboFood 更新美食
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 更新美食
// @Param id path int true "美食ID"
// @Param request body food_request.FoodComboRequest true "更新美食请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=food_resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/combo/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodComboController) UpdateComboFood(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.FoodComboRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}
	// 图片路径处理
	req.Image = util.GetImagePath(req.Image)

	_, err := ctrl.FoodComboService.UpdateComboFood(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}
