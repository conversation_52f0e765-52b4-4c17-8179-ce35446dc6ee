package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/food_request"
	"ros-api-go/internal/http/resource/food_resource"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/util"
)

type LunchBoxController struct {
	LunchBoxService           food_service.LunchBoxService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetLunchBoxList 获取餐盒列表
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 获取餐盒列表
// @Produce json
// @Param category_id query int false "餐盒分类ID"
// @Param search query string false "搜索关键词"
// @Param state query int false "状态"
// @Success 200 {object} util.ResponseResult{Data=util.PaginationResult{data=[]food_resource.LunchBoxResource}}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) GetLunchBoxList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.LunchBoxListRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	list, err := ctrl.LunchBoxService.GetLunchBoxList(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.LunchBoxResource{}).Collection(&ctx, list))
}

// CreateLunchBox 创建餐盒
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 创建餐盒
// @Param request body food_request.CreateLunchBoxRequest true "创建餐盒请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.LunchBoxResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) CreateLunchBox(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.CreateLunchBoxRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.LunchBoxService.CreateLunchBox(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// UpdateLunchBox 更新餐盒
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 更新餐盒
// @Param id path int true "餐盒ID"
// @Param request body food_request.UpdateLunchBoxRequest true "更新餐盒请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=food_resource.LunchBoxResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) UpdateLunchBox(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.UpdateLunchBoxRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.LunchBoxService.UpdateLunchBox(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// SaveLunchBoxSort 保存餐盒排序
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 保存餐盒排序
// @Param request body food_request.SaveLunchBoxSortRequest true "保存餐盒排序请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes/sort [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) SaveLunchBoxSort(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveLunchBoxSortRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.LunchBoxService.SaveLunchBoxSort(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// DeleteLunchBox 删除餐盒
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 删除餐盒
// @Param id path int true "餐盒ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) DeleteLunchBox(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	err := ctrl.LunchBoxService.DeleteLunchBox(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodUpdated)
}

// GetLunchBoxFoods 获取餐盒关联的美食列表
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 获取餐盒关联的美食列表
// @Param id path int true "餐盒ID"
// @Param page query int false "页码"
// @Param limit query int false "每页数量"
// @Produce json
// @Success 200 {object} util.ResponseResult{Data=util.PaginationResult{data=[]food_resource.FoodLunchBoxResource}}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes/{id}/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) GetLunchBoxFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id := util.StrToInt64(c.Param("id"))

	var req food_request.LunchBoxFoodsRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, list, err := ctrl.LunchBoxService.GetLunchBoxFoods(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&food_resource.FoodLunchBoxResource{}).Collection(&ctx, list))
}

// SaveLunchBoxFoods 关联菜品
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 关联菜品
// @Param request body food_request.SaveLunchBoxFoodsRequest true "关联菜品请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes/foods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) SaveLunchBoxFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.SaveLunchBoxFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.LunchBoxService.SaveLunchBoxFoods(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodRelationUpdated)
}

// RemoveLunchBoxFoods 解除关联
//
// @Tags 餐盒管理
// @Security ApiAuthToken
// @Summary 解除关联
// @Param request body food_request.RemoveLunchBoxFoodsRequest true "解除关联请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/lunch-boxes/foods [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/07/08 10:00"]
// @X-Version ["2.0"]
func (ctrl *LunchBoxController) RemoveLunchBoxFoods(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req food_request.RemoveLunchBoxFoodsRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	err := ctrl.LunchBoxService.RemoveLunchBoxFoods(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, handler.FoodRelationUpdated)
}
