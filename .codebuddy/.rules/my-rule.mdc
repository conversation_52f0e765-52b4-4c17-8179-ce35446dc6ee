# Data Types
- Use int64 for all integer types in the codebase.

# swagger API
- Use current timestamp for @X-Date, @X-Author ["Merdan"] for all API requests.

# Food Specs
- Food specs should have a sort field (numeric) that defaults to database value on creation, and list interfaces should return results sorted by this sort field.
- Food specification API should save spec data to foods table with zh_name/ug_name fields, use pid for binding with original dishes, set type=1 for food specifications, and accept price/vip_price from frontend.
- Food specification API should check if spec records already exist and update them instead of creating duplicates, implementing upsert logic.

# Method Groups
- Method groups should use name_zh and name_ug fields (both required) instead of single name field for bilingual support.

# Food-Method Relationships
- Food-method relationship tables should include price fields that default to method price, and resources should include an 'is_surcharge' indicator (price > 0 means surcharge, price = 0 means no surcharge).
- Food-method relationship table now includes group_id field (做法分组ID) in addition to existing fields, requiring updates to related interfaces and models.

# Food Additions and Lunch Boxes
- Food addition management uses foods table with type=2 for additions, food_addition table for relationships, and requires 6 specific interfaces: list, add/edit, get associated foods, associate, disassociate, and delete.
- Lunch box management features should reuse the same implementation logic as food additions, with categories and foods using type=3 (model.FoodTypeLunchBox) for lunch boxes.

# Food Feature Implementation
- For food-related features: implement files in their respective folders, add routes to /internal/route/food.go, create new resources instead of reusing existing food resources, but food_category resources can be reused.

# Error Messages
- Organize all error messages according to the multilingual file format.

# Database Transactions
- Use Trans.Exec method for database transactions instead of direct GORM transaction handling in services.

# Performance Optimization
- Optimize SQL queries executed in loops by using batch operations instead of individual queries for better performance.
- Prefer using GORM's built-in batch operations over raw SQL for database operations.

# Deletion and Dependencies
- When deleting parent entities, check for child dependencies (method groups should check for methods, methods should check for food associations), and list endpoints should include counts of related entities.

# Pagination
- Use WrapPageQuery from pkg/util/db.go for pagination instead of util.Paginate which doesn't exist.

# Translations
- Use i18n package for all user-facing strings, and use zh_CN and ug_CN translations for all languages.