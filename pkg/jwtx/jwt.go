package jwtx

import (
	"context"
	"errors"
	"go.uber.org/zap"
	"log"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt"
)

type Auther interface {
	// GenerateAlmasToken 生成自定义Token
	GenerateAlmasToken(ctx context.Context, user *model.UserTokenModel) (TokenInfo, error)
	// GenerateToken 生成Token
	GenerateToken(ctx context.Context, subject int64) (TokenInfo, error)
	// DestroyToken 销毁Token
	DestroyToken(ctx context.Context, accessToken string) error
	// ParseSubject 解析Token中的subject（用户ID）
	ParseSubject(ctx context.Context, accessToken string) (int64, error)
	// ParseToken 解析Token, 返回subject（用户ID）
	ParseToken(ctx context.Context, accessToken string) (*AlmasClaims, error)
	// Release any resources held by the JWTAuth instance.
	Release(ctx context.Context) error
}

const defaultKey = "CG24SDVP8OHPK395GB5G"

var ErrInvalidToken = errors.New("Invalid token")

type options struct {
	signingMethod  jwt.SigningMethod
	signingKey     []byte
	signingKey2    []byte
	keyFuncs       []func(*jwt.Token) (interface{}, error)
	expired        int
	refreshExpired int
	tokenType      string
}

type Option func(*options)

func SetSigningMethod(method jwt.SigningMethod) Option {
	return func(o *options) {
		o.signingMethod = method
	}
}

func SetSigningKey(key, oldKey string) Option {
	return func(o *options) {
		o.signingKey = []byte(key)
		if oldKey != "" && key != oldKey {
			o.signingKey2 = []byte(oldKey)
		}
	}
}

func SetExpired(expired int) Option {
	return func(o *options) {
		o.expired = expired
	}
}

func SetRefreshExpired(expired int) Option {
	return func(o *options) {
		o.refreshExpired = expired
	}
}

func New(store Storer, opts ...Option) *JWTAuth {
	o := options{
		tokenType:     "Bearer",
		expired:       7200,
		signingMethod: jwt.SigningMethodHS512,
		signingKey:    []byte(defaultKey),
	}

	for _, opt := range opts {
		opt(&o)
	}

	o.keyFuncs = append(o.keyFuncs, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrInvalidToken
		}
		return o.signingKey, nil
	})

	if o.signingKey2 != nil {
		o.keyFuncs = append(o.keyFuncs, func(t *jwt.Token) (interface{}, error) {
			if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, ErrInvalidToken
			}
			return o.signingKey2, nil
		})
	}

	log.Printf("JWTAuth signingKey: %v", string(o.signingKey))

	return &JWTAuth{
		opts:  &o,
		store: store,
	}
}

type JWTAuth struct {
	opts  *options
	store Storer
}

func (auth *JWTAuth) GenerateAlmasToken(ctx context.Context, user *model.UserTokenModel) (TokenInfo, error) {
	now := time.Now()
	Issuer := "ros-v3"
	ApiToken := user.UserToken[0:16]
	expiresAt := user.ExpiresAt.Unix()

	almasToken := AlmasClaims{
		Token:      ApiToken,
		Issuer:     Issuer,
		IssuedAt:   now.Unix(),
		ClientType: user.ClientType,
		ExpiresAt:  expiresAt,
		NotBefore:  now.Unix(),
		Id:         util.NewToken(),
		Subject:    user.UserID,
	}

	token := jwt.NewWithClaims(auth.opts.signingMethod, &almasToken)

	tokenStr, err := token.SignedString(auth.opts.signingKey)

	refreshExpiresAt := now.Add(time.Duration(auth.opts.refreshExpired) * time.Second).Unix()

	refreshToken := jwt.NewWithClaims(auth.opts.signingMethod, &AlmasClaims{
		Token:     ApiToken,
		Issuer:    Issuer,
		IssuedAt:  now.Unix(),
		ExpiresAt: expiresAt,
		NotBefore: now.Unix(),
		Id:        util.NewToken(),
		Subject:   user.ID,
	})

	refreshStr, err := refreshToken.SignedString(auth.opts.signingKey)

	if err != nil {
		return nil, err
	}

	tokenInfo := &tokenInfo{
		ExpiresAt:        expiresAt,
		TokenType:        auth.opts.tokenType,
		AccessToken:      tokenStr,
		RefreshToken:     refreshStr,
		RefreshExpiresAt: refreshExpiresAt,
	}

	err = auth.callStore(func(store Storer) error {
		expires := time.Until(time.Unix(almasToken.ExpiresAt, 0))
		tokenKey := strconv.FormatInt(almasToken.Subject, 10) + ":" + almasToken.Id
		return store.Set(ctx, tokenKey, expires)
	})
	if err != nil {
		return nil, err
	}

	return tokenInfo, nil
}

func (auth *JWTAuth) GenerateToken(ctx context.Context, subject int64) (TokenInfo, error) {
	now := time.Now()
	expiresAt := now.Add(time.Duration(auth.opts.expired) * time.Second).Unix()

	token := jwt.NewWithClaims(auth.opts.signingMethod, &AlmasClaims{
		Issuer:    "ros-api",
		IssuedAt:  now.Unix(),
		ExpiresAt: expiresAt,
		NotBefore: now.Unix(),
		Id:        util.NewToken(),
		Subject:   subject,
	})

	tokenStr, err := token.SignedString(auth.opts.signingKey)

	refreshExpiresAt := now.Add(time.Duration(auth.opts.refreshExpired) * time.Second).Unix()

	refreshToken := jwt.NewWithClaims(auth.opts.signingMethod, &AlmasClaims{
		Issuer:    "ros-api",
		IssuedAt:  now.Unix(),
		ExpiresAt: expiresAt,
		NotBefore: now.Unix(),
		Id:        util.NewToken(),
		Subject:   subject,
	})

	refreshStr, err := refreshToken.SignedString(auth.opts.signingKey)

	if err != nil {
		return nil, err
	}

	tokenInfo := &tokenInfo{
		ExpiresAt:        expiresAt,
		TokenType:        auth.opts.tokenType,
		AccessToken:      tokenStr,
		RefreshToken:     refreshStr,
		RefreshExpiresAt: refreshExpiresAt,
	}
	return tokenInfo, nil
}

// ParseToken 解析token， 并返回claims
func (auth *JWTAuth) ParseToken(ctx context.Context, accessToken string) (*AlmasClaims, error) {
	var (
		token *jwt.Token
		err   error
	)

	for _, keyFunc := range auth.opts.keyFuncs {
		token, err = jwt.ParseWithClaims(accessToken, &AlmasClaims{}, keyFunc)
		if err != nil || token == nil || !token.Valid {
			continue
		}
		break
	}

	if err != nil || token == nil || !token.Valid {
		return nil, ErrInvalidToken
	}

	claims := token.Claims.(*AlmasClaims)

	err = claims.Valid()
	if err != nil {
		logging.Context(ctx).Error("JWTAuth:Invalid token", zap.String("token", accessToken), zap.Any("claims", claims), zap.String("error", err.Error()))
		return nil, ErrInvalidToken
	}

	tokenKey := strconv.FormatInt(claims.Subject, 10) + ":" + claims.Id

	// 检查token是否存在
	err = auth.callStore(func(store Storer) error {
		if exists, err := store.Check(ctx, tokenKey); err != nil {
			return err
		} else if !exists { // token不存在
			logging.Context(ctx).Error("JWTAuth:Invalid token", zap.String("token", accessToken), zap.Any("claims", claims), zap.String("error", "Token不在缓存中"))
			return ErrInvalidToken
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return token.Claims.(*AlmasClaims), nil
}

func (auth *JWTAuth) callStore(fn func(Storer) error) error {
	if store := auth.store; store != nil {
		return fn(store)
	}
	return nil
}

func (auth *JWTAuth) DestroyToken(ctx context.Context, tokenStr string) error {
	claims, err := auth.ParseToken(ctx, tokenStr)
	if err != nil {
		return err
	}

	return auth.callStore(func(store Storer) error {
		tokenKey := strconv.FormatInt(claims.Subject, 10) + ":" + claims.Id
		return store.Delete(ctx, tokenKey)
	})
}

func (auth *JWTAuth) ParseSubject(ctx context.Context, tokenStr string) (int64, error) {
	if tokenStr == "" {
		return 0, ErrInvalidToken
	}

	// 解析token
	claims, err := auth.ParseToken(ctx, tokenStr)
	if err != nil {
		return 0, err
	}

	return claims.Subject, nil
}

func (auth *JWTAuth) Release(ctx context.Context) error {
	return auth.callStore(func(store Storer) error {
		return store.Close(ctx)
	})
}
