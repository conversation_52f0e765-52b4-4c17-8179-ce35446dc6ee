package alipayx

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay/v3"
	"net/http"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"time"
)

type AlipayClient struct {
	*alipay.ClientV3
}

func NewPaymentClient() (*AlipayClient, error) {

	alipayConfig := config.C.Alipay
	payment := alipayConfig.Payment

	client, err := alipay.NewClientV3(payment.AppID, payment.AppPrivateKey, true)
	if err != nil {
		return nil, err
	}

	if config.C.IsDebug() {
		client.DebugSwitch = gopay.DebugOn
	}

	client.SetAESKey(payment.EncryptKey)

	// 应用公钥证书
	appCert, err := base64.StdEncoding.DecodeString(payment.AppCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError:AppCert"+err.Error())
	}

	// 支付宝根证书s
	alipayRootCert, err := base64.StdEncoding.DecodeString(alipayConfig.RootCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError:RootCert"+err.Error())
	}

	// 支付宝公钥证书
	alipayPublicCert, err := base64.StdEncoding.DecodeString(alipayConfig.PublicCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError:PublicCert"+err.Error())
	}

	// 传入证书内容
	err = client.SetCert(appCert, alipayRootCert, alipayPublicCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError"+err.Error())
	}

	paymentClient := AlipayClient{client}

	return &paymentClient, nil
}

// TradeCreate 统一收单交易创建接口 alipay.trade.create
// StatusCode = 200 is success
func (a *AlipayClient) TradeCreate(ctx context.Context, bm gopay.BodyMap) (aliRsp *alipay.TradeCreateRsp, err error) {
	err = bm.CheckEmptyError("out_trade_no", "total_amount", "subject", "product_code", "op_app_id")
	if err != nil {
		return nil, err
	}
	resMap := make(map[string]interface{})
	res, err := a.DoAliPayAPISelfV3(ctx, alipay.MethodPost, "/v3/alipay/trade/create", bm, &resMap)
	if err != nil {
		return nil, err
	}
	bs, err := json.Marshal(resMap)
	if err != nil {
		return nil, err
	}
	aliRsp = &alipay.TradeCreateRsp{
		StatusCode: res.StatusCode,
	}
	if res.StatusCode != http.StatusOK {
		if err = json.Unmarshal(bs, &aliRsp.ErrResponse); err != nil {
			return nil, fmt.Errorf("[%w], bytes: %s", gopay.UnmarshalErr, string(bs))
		}
		return aliRsp, nil
	}
	if err = json.Unmarshal(bs, aliRsp); err != nil {
		return nil, fmt.Errorf("[%w], bytes: %s", gopay.UnmarshalErr, string(bs))
	}
	return aliRsp, err
}

// GetPaymentNotifyUrl 获取微信支付通知地址
// paymentNo 支付订单号
func GetPaymentNotifyUrl(orderType int64, paymentNo string) string {
	if orderType == consts.ORDER_TYPE_ORDER {
		return GetOrderNotifyUrl(paymentNo)
	}
	if orderType == consts.ORDER_TYPE_DEBT_REPAYMENT {
		return GetDebtRepaymentNotifyUrl(paymentNo)
	}
	return GetRechargeNotifyUrl(paymentNo)
}

// GetOrderNotifyUrl 获取微信退款通知地址
func GetOrderNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/alipay/payment-notify/" + paymentNo
}

// GetRefundNotifyUrl 获取微信退款通知地址
func GetRefundNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/alipay/refund-notify/" + paymentNo
}

// GetRechargeNotifyUrl 获取微信退款通知地址
func GetRechargeNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/alipay/recharge-notify/" + paymentNo
}

// GetDebtRepaymentNotifyUrl 获取微信赊账还款通知地址
func GetDebtRepaymentNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/alipay/debt-repayment-notify/" + paymentNo
}

// ParseDateTimeV3 解析时间字符串
func ParseDateTimeV3(alipayTime string) (time.Time, error) {
	// 北京时间
	location, _ := time.LoadLocation("Asia/Shanghai")
	return time.ParseInLocation(time.DateTime, alipayTime, location)
}
