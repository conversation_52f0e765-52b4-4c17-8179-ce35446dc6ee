package alipayx

import (
	"encoding/base64"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay/v3"
	"ros-api-go/internal/config"
	"ros-api-go/pkg/errors"
)

type AlipayMiniClient struct {
	*alipay.ClientV3
}

func NewMiniClient() (*AlipayMiniClient, error) {

	alipayConfig := config.C.Alipay
	mini := alipayConfig.Mini

	client, err := alipay.NewClientV3(mini.AppID, mini.AppPrivateKey, true)
	if err != nil {
		return nil, err
	}

	if config.C.IsDebug() {
		client.DebugSwitch = gopay.DebugOn
	}

	client.SetAESKey(mini.EncryptKey)

	// 应用公钥证书
	appCert, err := base64.StdEncoding.DecodeString(mini.AppCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError"+err.Error())
	}

	// 支付宝根证书s
	alipayRootCert, err := base64.StdEncoding.DecodeString(alipayConfig.RootCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError"+err.Error())
	}
	// 支付宝公钥证书
	alipayPublicCert, err := base64.StdEncoding.DecodeString(alipayConfig.PublicCert)

	// 传入证书内容
	err = client.SetCert(appCert, alipayRootCert, alipayPublicCert)
	if err != nil {
		return nil, errors.BadRequest("", "AlipayCertError"+err.Error())
	}
	miniClient := AlipayMiniClient{client}
	return &miniClient, nil
}
