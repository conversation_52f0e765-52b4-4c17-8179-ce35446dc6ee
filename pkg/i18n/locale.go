package i18n

import (
	"context"
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/validation"
	"strings"
)

// Options Localizer options
type Options struct {
	DefaultLang   string // 默认语言
	SupportedLang string // 支持语言
	FilePath      string // multilingual file directory
}

var l *localize

// Localizer middleware
func Localizer(opt *Options) gin.HandlerFunc {
	defaultStr := strings.TrimSpace(opt.DefaultLang)
	supportStr := strings.TrimSpace(opt.SupportedLang)
	filePathStr := strings.TrimSpace(opt.FilePath)
	if len(defaultStr) == 0 || len(supportStr) == 0 {
		panic("bad defaultLang or SupportedLang")
	}

	l = NewLocalize(defaultStr, supportStr, filePathStr)

	return func(c *gin.Context) {
		acceptLang := c.GetHeader("Accept-Language")
		if acceptLang == "" {
			acceptLang = defaultStr
		}
		localizer := l.UserLocalize(acceptLang)
		// 表单验证语言设置
		translator, _ := validation.GetValidatorLocale(acceptLang)
		c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), consts.LocaleKey, acceptLang))
		c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), consts.LocalizerKey, localizer))
		c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), consts.ValidationTranslatorKey, translator))

		c.Next()
	}
}

func IsZh(ctx *context.Context) bool {
	c := *ctx
	value := c.Value(consts.LocaleKey)
	return value == nil || value == "zh-CN"
}

func Language(ctx *context.Context) string {
	c := *ctx
	value := c.Value(consts.LocaleKey)
	if value == nil || value == "zh-CN" {
		return "zh"
	}
	return "ug"
}
