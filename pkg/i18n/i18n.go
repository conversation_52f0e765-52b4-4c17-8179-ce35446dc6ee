package i18n

import (
	"context"
	"fmt"
	"os"
	"path"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/encoding/yaml"
	"strings"

	"golang.org/x/text/language"
)

type message map[string]any

type localize struct {
	defaultLang   language.Tag
	supportedLang []language.Tag
	messages      map[language.Tag]message
}

func (l *localize) matchUserLang(acceptStr string) language.Tag {
	acceptLangs, _, err := language.ParseAcceptLanguage(acceptStr)
	if err != nil {
		return l.defaultLang
	}

	for _, acceptLang := range acceptLangs {
		for _, supportLang := range l.supportedLang {
			if strings.HasPrefix(supportLang.String(), acceptLang.String()) {
				return supportLang
			}
		}
	}

	return l.defaultLang
}

func (l *localize) UserLocalize(acceptStr string) *userLocalize {
	userLang := l.matchUserLang(acceptStr)

	return &userLocalize{
		l,
		userLang,
	}
}

func NewLocalize(defaultStr, supportStr, filePath string) *localize {
	defaultLang := language.Make(defaultStr)
	var supportedLang []language.Tag
	for _, s := range strings.Split(supportStr, ",") {
		supportedLang = append(supportedLang, language.Make(strings.TrimSpace(s)))
	}
	if !tagContains(supportedLang, defaultLang) {
		panic("supportedLang must contains defaultLang")
	}

	messages := make(map[language.Tag]message, len(supportedLang))
	fmt.Println("load lang")
	for _, lang := range supportedLang {
		var message message
		f, err := os.ReadFile(path.Join(filePath, fmt.Sprintf("%s.yml", lang)))
		if err != nil {
			panic(err)
		}
		if err := yaml.Unmarshal(f, &message); err != nil {
			panic(fmt.Sprintf("语言包读取失败：%v", err))
		}
		messages[lang] = message
	}

	//fmt.Println("=================初始化语言包=================")

	return &localize{defaultLang, supportedLang, messages}
}

func tagContains(tags []language.Tag, t language.Tag) bool {
	for _, tag := range tags {
		if tag == t {
			return true
		}
	}
	return false
}

type userLocalize struct {
	l        *localize
	userLang language.Tag
}

func removeIndex(s string) string {
	// 找到第一个 '[' 的位置
	openBracket := strings.Index(s, "[")
	if openBracket == -1 {
		// 如果没有找到 '['，则直接返回原字符串
		return s
	}
	// 截取从开始到第一个 '[' 之前的部分
	return s[:openBracket]
}

// getMessage 递归获取多级消息
func (l *localize) getMessage(msg message, keys []string) (string, bool) {
	if len(keys) == 0 {
		return "", false
	}

	key := removeIndex(keys[0])

	// 获取第一个key的值
	value, exists := msg[key]
	if !exists {
		return "", false
	}

	// 如果是最后一个key，且值是字符串，直接返回
	if len(keys) == 1 {
		if str, ok := value.(string); ok {
			return str, true
		}
		return "", false
	}

	// 如果还有下一级，且当前值是map，继续递归
	if nextMsg, ok := value.(message); ok {
		return l.getMessage(nextMsg, keys[1:])
	}

	return "", false
}

func (u *userLocalize) getMsg(tag string, args ...any) (msg string) {
	if u.l == nil {
		return
	}
	if tag == "" {
		return ""
	}
	// 将tag按点分割成多级key
	keys := strings.Split(tag, ".")

	// 先尝试从用户语言获取
	if s, ok := u.l.getMessage(u.l.messages[u.userLang], keys); ok {
		msg = s
	} else {
		// 如果用户语言没有，从默认语言获取
		if s, ok := u.l.getMessage(u.l.messages[u.l.defaultLang], keys); ok {
			msg = s
		} else {
			msg = tag
		}
	}
	if msg == "" {
		msg = tag
	}
	if len(msg) > 0 && len(args) > 0 {
		msg = fmt.Sprintf(msg, args...)
	}

	return
}

// Msg Get localizer message
func Msg(ctx *context.Context, tag string, args ...any) string {
	localizer := (*ctx).Value(consts.LocalizerKey).(*userLocalize)
	return localizer.getMsg(tag, args...)
}

func MsgByLang(lang string, tag string, args ...any) (msg string) {
	if l == nil {
		return
	}

	if s, ok := l.messages[language.Make(lang)][tag]; ok {
		msg = s.(string)
	}

	if msg == "" {
		msg = tag
	}
	if len(msg) > 0 && len(args) > 0 {
		msg = fmt.Sprintf(msg, args...)
	}

	return
}
