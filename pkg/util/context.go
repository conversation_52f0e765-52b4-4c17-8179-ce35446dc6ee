package util

import (
	"context"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"ros-api-go/pkg/encoding/json"
	"time"
)

type (
	traceIDCtx          struct{} // 跟踪ID
	transCtx            struct{} // 事务对象
	rowLockCtx          struct{} // 行锁标记
	userIDCtx           struct{} // 用户ID
	userNameCtx         struct{} // 用户名
	wechatUserIDCtx     struct{} // 用户ID(微信)
	serverMerchantNoCtx struct{} // 服务商商户号(本地服务部分)
	userTokenCtx        struct{} // 用户token
	userCacheCtx        struct{} // 用户缓存
	merchantCacheCtx    struct{} // 商家缓存
)

// NewTraceID 为context添加traceID (用于跟踪错误)
func NewTraceID(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, traceIDCtx{}, traceID)
}

// FromTraceID 从context中获取traceID
func FromTraceID(ctx context.Context) string {
	v := ctx.Value(traceIDCtx{})
	if v != nil {
		return v.(string)
	}
	return ""
}

// NewTrans 为context添加事务对象(数据库)
func NewTrans(ctx context.Context, db *gorm.DB) context.Context {
	return context.WithValue(ctx, transCtx{}, db)
}

// FromTrans 从context中获取事务对象(数据库)
func FromTrans(ctx context.Context) (*gorm.DB, bool) {
	v := ctx.Value(transCtx{})
	if v != nil {
		return v.(*gorm.DB), true
	}
	return nil, false
}

// NewRowLock 为context添加行锁标记
func NewRowLock(ctx context.Context) context.Context {
	return context.WithValue(ctx, rowLockCtx{}, true)
}

// FromRowLock 从context中获取行锁标记From
func FromRowLock(ctx context.Context) bool {
	v := ctx.Value(rowLockCtx{})
	return v != nil && v.(bool)
}

// NewUserID 为context添加用户ID
func NewUserID(ctx context.Context, userID int64) context.Context {
	return context.WithValue(ctx, userIDCtx{}, userID)
}

// FromUserID 从context中获取用户ID（int64）
func FromUserID(ctx context.Context) int64 {
	v := ctx.Value(userIDCtx{})
	if v != nil {
		return v.(int64)
	}
	return 0
}

// NewUserName 为context添加用户ID
func NewUserName(ctx context.Context, userName string) context.Context {
	return context.WithValue(ctx, userNameCtx{}, userName)
}

// FromUserName 从context中获取用户ID（int64）
func FromUserName(ctx context.Context) string {
	v := ctx.Value(userNameCtx{})
	if v != nil {
		return v.(string)
	}
	return "unknown"
}

// NewWechatUserID 为context添加用户ID
func NewWechatUserID(ctx context.Context, userID int64) context.Context {
	return context.WithValue(ctx, wechatUserIDCtx{}, userID)
}

// FromWechatUserID 从context中获取用户ID（int64）
func FromWechatUserID(ctx context.Context) int64 {
	v := ctx.Value(wechatUserIDCtx{})
	if v != nil {
		return v.(int64)
	}
	return 0
}

// FromUserIDStr 从context中获取用户ID (string)
func FromUserIDStr(ctx context.Context) string {
	v := ctx.Value(userIDCtx{})
	if v != nil {
		return v.(string)
	}
	return ""
}

// NewServerMerchantNo 为context添加服务商商户号
func NewServerMerchantNo(ctx context.Context, MerchantNo string) context.Context {
	return context.WithValue(ctx, serverMerchantNoCtx{}, MerchantNo)
}

// FromServerMerchantNo 从context中获取服务商商户号
func FromServerMerchantNo(ctx context.Context) string {
	v := ctx.Value(serverMerchantNoCtx{})
	if v != nil {
		return v.(string)
	}
	return ""
}

// NewUserToken 为context添加用户token
func NewUserToken(ctx context.Context, userToken string) context.Context {
	return context.WithValue(ctx, userTokenCtx{}, userToken)
}

// FromUserToken 从context中获取用户token
func FromUserToken(ctx context.Context) string {
	v := ctx.Value(userTokenCtx{})
	if v != nil {
		return v.(string)
	}
	return ""
}

// UserCache 用户缓存
type UserCache struct {
	RoleIDs []int64 `json:"rids"`
}

// ParseUserCache 解析用户缓存
func ParseUserCache(s string) UserCache {
	var a UserCache
	if s == "" {
		return a
	}

	_ = json.Unmarshal([]byte(s), &a)
	return a
}

// String 序列化用户缓存
func (a UserCache) String() string {
	return json.MarshalToString(a)
}

// NewUserCache 为context添加用户缓存
func NewUserCache(ctx context.Context, userCache UserCache) context.Context {
	return context.WithValue(ctx, userCacheCtx{}, userCache)
}

// FromUserCache 从context中获取用户缓存
func FromUserCache(ctx context.Context) UserCache {
	v := ctx.Value(userCacheCtx{})
	if v != nil {
		return v.(UserCache)
	}
	return UserCache{}
}

type MerchantCache struct {
	ID                 int64      // ID
	No                 string     // 商户号
	NameUg             string     // 商户名称
	NameZh             string     // 商户名称
	ExpiredAt          *time.Time // 过期时间
	RefundPassword     string     // 退款密码
	CancelPassword     string     // 取消密码
	WechatPaymentType  int64      // 微信支付类型
	SubMerchantNo      string     // 子商户号
	MchID              string     // 商户号
	SubMchID           string     // 子商户号
	AlipaySubMchID     string     // 支付宝子商户号
	AlipayAppAuthToken string     // 支付宝应用授权token
	State              int64      // 商户状态 1 启用 2 停用
}

func (a MerchantCache) String() string {
	return json.MarshalToString(a)
}

func ParseMerchantCache(s string) MerchantCache {
	var a MerchantCache
	if s == "" {
		return a
	}
	_ = json.Unmarshal([]byte(s), &a)
	return a
}

func NewMerchantCache(ctx context.Context, merchantCache MerchantCache) context.Context {
	return context.WithValue(ctx, merchantCacheCtx{}, merchantCache)
}

func FromMerchantCache(ctx context.Context) MerchantCache {
	v := ctx.Value(merchantCacheCtx{})
	if v != nil {
		return v.(MerchantCache)
	}
	return MerchantCache{}
}

func GetMerchantNo(c *gin.Context) string {
	return c.Request.Header.Get("Merchantno")
}
