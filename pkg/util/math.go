package util

import (
	"github.com/shopspring/decimal"
	"math"
)

func Min[T ~float64 | ~int | ~int64](a, b T) T {
	if a < b {
		return a
	}
	return b
}

func Round(price float64) float64 {
	return math.Round(MultiplyFloat(price, 100)) / 100
}

func RoundUp(price float64) float64 {
	return DivideFloat(math.Round(price*100), 100)
}

// AddFloat decimal类型加法
// return d1 + d2
func AddFloat(d1, d2 float64) float64 {
	decimalD1 := decimal.NewFromFloat(d1)
	decimalD2 := decimal.NewFromFloat(d2)
	decimalResult := decimalD1.Add(decimalD2)
	float64Result, _ := decimalResult.Float64()
	return float64Result
}
func AddFloatMore(d1 float64, floats ...float64) float64 {
	result := d1
	for _, f := range floats {
		result = AddFloat(result, f)
	}
	return result
}

// SubtractFloat decimal类型减法
// return d1 - d2
func SubtractFloat(d1, d2 float64) float64 {
	decimalD1 := decimal.NewFromFloat(d1)
	decimalD2 := decimal.NewFromFloat(d2)
	decimalResult := decimalD1.Sub(decimalD2)
	float64Result, _ := decimalResult.Float64()
	return float64Result
}
func SubtractFloatMore(d1 float64, floats ...float64) float64 {
	result := d1
	for _, f := range floats {
		result = SubtractFloat(result, f)
	}
	return result
}

// MultiplyFloat decimal类型乘法
// return d1 * d2
func MultiplyFloat(d1, d2 float64) float64 {
	decimalD1 := decimal.NewFromFloat(d1)
	decimalD2 := decimal.NewFromFloat(d2)
	decimalResult := decimalD1.Mul(decimalD2)
	float64Result, _ := decimalResult.Float64()
	return float64Result
}

// DivideFloat decimal类型除法
// return d1 / d2
func DivideFloat(d1, d2 float64) float64 {
	decimalD1 := decimal.NewFromFloat(d1)
	decimalD2 := decimal.NewFromFloat(d2)
	decimalResult := decimalD1.Div(decimalD2)
	float64Result, _ := decimalResult.Float64()
	return float64Result
}
