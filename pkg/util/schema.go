package util

import (
	"database/sql/driver"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"strings"
	"time"
)

const (
	ReqBodyKey        = "req-body"
	ResBodyKey        = "res-body"
	TreePathDelimiter = "."
)

// ResponseResult 响应结果结构体
type ResponseResult struct {
	Message string            `json:"message"`
	Data    interface{}       `json:"data,omitempty"`
	Error   interface{}       `json:"errors,omitempty"`
	Meta    *PaginationResult `json:"meta,omitempty"`
}

// PagedResult 分页结果结构体
type PagedResult struct {
	Data       interface{}       `json:"data"`
	PageResult *PaginationResult `json:"paginationResult"`
}

// PaginationResult 分页结果结构体
type PaginationResult struct {
	Total int64 `json:"total"`
	Page  int   `json:"current_page"`
	Limit int   `json:"per_page"`
}

type PaginationParam struct {
	GetAll    bool `form:"-"`
	OnlyCount bool `form:"-"`
	Page      int  `form:"page"`
	Limit     int  `form:"limit" binding:"max=100"`
}

type Direction string

const (
	ASC  Direction = "ASC"
	DESC Direction = "DESC"
)

// SortParam 排序参数
// 格式为 field1,-field2,field3
// 其中 field1 为正序排序，-field2 为倒序排序，field3 为正序排序
type SortParam string

// split 按逗号分割字符串并返回切片
func (a SortParam) split() []string {
	return strings.Split(string(a), ",")
}

// Apply 应用排序参数到 gorm.DB 对象中
// defaultSort 为默认排序字段，如果没有指定排序字段，则使用默认排序字段
// availableFields 为可排序的字段列表
func (a SortParam) Apply(db *gorm.DB, defaultSort SortParam, availableFields ...string) {
	// 如果没有指定排序字段，则使用默认排序字段
	if len(a) == 0 {
		if len(defaultSort) > 0 {
			defaultSort.Apply(db, "", availableFields...)
		}
		return
	}
	// 解析排序字段
	fields := a.split()
	sql := ""
	for _, field := range fields {
		direction := ASC
		if field[0] == '-' {
			direction = DESC
			field = field[1:]
		}
		if !lo.Contains(availableFields, field) {
			continue
		}
		if sql != "" {
			sql += ", "
		}
		sql += fmt.Sprintf("%s %s", field, direction)
	}
	if sql != "" {
		db = db.Order(sql)
	} else {
		defaultSort.Apply(db, "", availableFields...)
	}
}

// JSON 自定义类型用于存储JSON数据
type JSON map[string]interface{}

// Scan 从数据库读取JSON数据并解析到结构体
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.BadRequest("invalid JSON", "invalid JSON")
	}
	return json.Unmarshal(bytes, j)
}

// Value 将结构体序列化为JSON字符串并保存到数据库
func (j JSON) Value() (driver.Value, error) {
	return json.Marshal(j)
}

// TArray 自定义类型，用于表示 JSON 数组
type TArray[T any] []T

// Value 实现 driver.Valuer 接口，将 []string 转换为 JSON 字符串
func (s TArray[T]) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan 实现 sql.Scanner 接口，将 JSON 字符串转换为 []string
func (s *TArray[T]) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal JSONB value: %v", value)
	}
	return json.Unmarshal(b, s)
}

type Datetime time.Time

func (d Datetime) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%s\"", time.Time(d).Format("2006-01-02 15:04:05"))), nil
}
