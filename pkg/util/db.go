package util

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"reflect"
)

type Trans struct {
	DB *gorm.DB
}

type TransFunc func(context.Context) error

func (a *Trans) Exec(ctx context.Context, fn TransFunc) error {
	if _, ok := FromTrans(ctx); ok {
		return fn(ctx)
	}

	return a.DB.Transaction(func(db *gorm.DB) error {
		return fn(NewTrans(ctx, db))
	})
}

func GetDB(ctx context.Context, defDB *gorm.DB) *gorm.DB {
	db := defDB
	if tdb, ok := FromTrans(ctx); ok {
		db = tdb
	}
	if FromRowLock(ctx) {
		db = db.Clauses(clause.Locking{Strength: "UPDATE"})
	}
	return db.WithContext(ctx)
}

func WrapPageQuery(ctx context.Context, db *gorm.DB, pp PaginationParam, out interface{}) (*PaginationResult, error) {
	if pp.Page <= 0 {
		pp.Page = 1
	}
	if pp.Limit <= 0 {
		pp.Limit = 10
	}
	if pp.Limit > 100 {
		pp.Limit = 100
	}
	if pp.OnlyCount {
		var count int64
		err := db.Count(&count).Error
		if err != nil {
			return nil, err
		}
		return &PaginationResult{Total: count}, nil
	}
	total, err := FindPage(ctx, db, pp, out)
	if err != nil {
		return nil, err
	}

	return &PaginationResult{
		Total: total,
		Page:  pp.Page,
		Limit: pp.Limit,
	}, nil
}

func FindPage(ctx context.Context, db *gorm.DB, pp PaginationParam, out interface{}) (int64, error) {
	db = db.WithContext(ctx)
	var err error
	var count int64 = -1
	if !pp.GetAll {
		err := db.Count(&count).Error
		if err != nil {
			return 0, err
		} else if count == 0 {
			return count, nil
		}
		current, pageSize := pp.Page, pp.Limit
		if pageSize > 100 {
			pageSize = 100
		}
		if current > 0 && pageSize > 0 {
			db = db.Offset((current - 1) * pageSize).Limit(pageSize)
		} else if pageSize > 0 {
			db = db.Limit(pageSize)
		}
	}

	err = db.Find(out).Error
	if count < 0 {
		v := reflect.ValueOf(out)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}
		if v.Kind() == reflect.Slice || v.Kind() == reflect.Array {
			count = int64(v.Len())
		} else {
			return 0, nil
		}
	}
	return count, err
}

func FindOne(ctx context.Context, db *gorm.DB, out interface{}) (bool, error) {
	db = db.WithContext(ctx)
	result := db.First(out)
	if err := result.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func Exists(ctx context.Context, db *gorm.DB) (bool, error) {
	db = db.WithContext(ctx)
	var count int64
	result := db.Count(&count)
	if err := result.Error; err != nil {
		return false, err
	}
	return count > 0, nil
}
