package util

import (
	"fmt"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"net"
	"net/http"
	"reflect"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/i18n"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	stdError "github.com/pkg/errors"
	"go.uber.org/zap"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
)

// GetToken Get access token from header or query parameter
func GetToken(c *gin.Context) string {
	var token string
	auth := c.GetHeader("Authorization")
	prefix := "Bearer "

	if auth != "" && strings.HasPrefix(auth, prefix) {
		token = auth[len(prefix):]
	} else {
		token = auth
	}

	if token == "" {
		token = c.Query("accessToken")
	}

	return token
}

// GetBodyData Get body data from context
func GetBodyData(c *gin.Context) []byte {
	if v, ok := c.Get(ReqBodyKey); ok {
		if b, ok := v.([]byte); ok {
			return b
		}
	}
	return nil
}

// Parse body json data to struct
func ParseJSON(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		if stdError.As(err, &validator.ValidationErrors{}) {
			return err
		}
		return errors.BadRequest("", "FailedToParseJson", err)
	}
	return nil
}

// Parse query parameter to struct
func ParseQuery(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		if stdError.As(err, &validator.ValidationErrors{}) {
			return err
		}
		return errors.BadRequest("", "FailedToParseQuery", err)
	}
	return nil
}

// Parse body form data to struct
func ParseForm(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindWith(obj, binding.Form); err != nil {
		return errors.BadRequest("", "Failed to parse form: %s", err.Error())
	}
	return nil
}

func parseMessageParams(msgParams []any, defaultMsg string) (string, []any) {
	message := defaultMsg
	params := make([]any, 0)
	if len(msgParams) > 0 {
		message = msgParams[0].(string)
		params = msgParams[1:]
	}
	return message, params
}

// Response json data with status code
func ResJSON(c *gin.Context, status int, v interface{}) {
	buf, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}

	c.Set(ResBodyKey, buf)
	c.Data(status, "application/json; charset=utf-8", buf)
	c.Abort()
}

func ResSuccess(c *gin.Context, v interface{}, msgParams ...any) {
	message, params := parseMessageParams(msgParams, "Success")
	ctx := c.Request.Context()
	ResJSON(c, http.StatusOK, ResponseResult{
		Message: i18n.Msg(&ctx, message, params...),
		Data:    v,
	})
}

func ResOK(c *gin.Context, msgParams ...any) {
	message, params := parseMessageParams(msgParams, "Success")
	ctx := c.Request.Context()
	ResJSON(c, http.StatusOK, ResponseResult{
		Message: i18n.Msg(&ctx, message, params...),
	})
}

func ResPage(c *gin.Context, v interface{}, meta *PaginationResult, msgParams ...any) {

	reflectValue := reflect.Indirect(reflect.ValueOf(v))
	if reflectValue.IsNil() {
		v = make([]interface{}, 0)
	}
	message, params := parseMessageParams(msgParams, "GetSuccess")
	ctx := c.Request.Context()
	ResJSON(c, http.StatusOK, ResponseResult{
		Message: i18n.Msg(&ctx, message, params...),
		Data:    v,
		Meta:    meta,
	})
}

// ResError 统一返回错误
func ResError(c *gin.Context, err error, status ...int) {
	ctx := c.Request.Context()
	var ierr *errors.Error

	if stdError.As(err, &validator.ValidationErrors{}) {
		validationErrors := gin.H{}
		message := ""
		for index, fieldError := range err.(validator.ValidationErrors) {
			fieldName := fieldError.Field()                 // 格式：Request名称.字段名
			namespace := "Fields." + fieldError.Namespace() // 格式：Request名称
			// 尝试获取字段的国际化名称
			translatedFieldName := i18n.Msg(&ctx, namespace)

			// 如果没有找到翻译，使用原始字段名
			if translatedFieldName == namespace {
				namespace = namespace + "Name"
				translatedFieldName = i18n.Msg(&ctx, namespace)
				if translatedFieldName == namespace {
					translatedFieldName = fieldName
				}
			}
			msg := fieldError.Translate(c.Request.Context().Value(consts.ValidationTranslatorKey).(ut.Translator))
			msg = strings.Replace(msg, fieldName, translatedFieldName, -1)
			validationErrors[fieldName] = msg
			if index == 0 {
				message = msg
			}
			break
		}
		logging.Context(c.Request.Context()).Warn("Validation failed", zap.Any("errors", validationErrors))
		// 返回验证错误信息
		ResJSON(c, http.StatusUnprocessableEntity, ResponseResult{Message: message, Error: validationErrors})
		return
	}

	if e, ok := errors.As(err); ok {
		ierr = e
	} else {
		ierr = errors.FromError(err)
	}

	code := int(ierr.Code)
	if len(status) > 0 {
		code = status[0]
	}
	// 如果没有状态码，默认500
	if code == 0 {
		code = http.StatusInternalServerError
	}

	if code >= 500 {
		ctx = logging.NewTag(ctx, logging.TagKeySystem)
		ctx = logging.NewStack(ctx, fmt.Sprintf("%+v", err))
		logging.Context(ctx).Error("InternalServerError", zap.Error(err))
		// 如果是服务器内部错误,正式环境，返回默认的服务器内部错误信息
		if !config.C.General.Debug {
			ierr = errors.InternalServerError("", "InternalServerError")
		}
	}

	ierr.Code = int32(code)
	ResJSON(c, code, ResponseResult{Message: i18n.Msg(&ctx, ierr.Format, ierr.Params...), Error: ierr})
}

func GetClientIP(r *http.Request) string {

	trustedProxies := config.C.General.HTTP.TrustedProxies
	remoteAddr, _, _ := net.SplitHostPort(r.RemoteAddr)
	// 检查 当前IP 是否在信任代理列表中
	isTrusted := false
	for _, proxy := range trustedProxies {
		if remoteAddr == proxy || proxy == "*" {
			isTrusted = true
			break
		}
	}
	// 如果不在信任代理列表中，直接返回 RemoteAddr
	if isTrusted {
		// 1. 优先尝试 X-Forwarded-For
		xForwardedFor := r.Header.Get("X-Forwarded-For")
		if xForwardedFor != "" {
			// 分割所有 IP 地址
			ips := strings.Split(xForwardedFor, ",")
			for _, ip := range ips {
				// 没有信任代理配置时取第一个 IP
				clientIP := strings.TrimSpace(ip)
				if clientIP != "" {
					return clientIP
				}
			}
		}

		// 2. 尝试 X-Real-IP
		xRealIP := r.Header.Get("X-Real-IP")
		if xRealIP != "" {
			return xRealIP
		}
	}

	// 3. 回退到 RemoteAddr（需去除端口）
	return remoteAddr
}
