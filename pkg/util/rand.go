package util

import (
	"encoding/binary"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"
)

// The RandomizedIPAddr function generates a random IP address.
func RandomizedIPAddr() string {
	raw := make([]byte, 4)
	rd := rand.New(rand.NewSource(time.Now().UnixNano()))
	binary.LittleEndian.PutUint32(raw, rd.Uint32())

	ips := make([]string, len(raw))
	for i, b := range raw {
		ips[i] = strconv.FormatInt(int64(b), 10)
	}
	return strings.Join(ips, ".")
}

// RandomString 随机生成字符串
func RandomString(length int) string {
	// 生成随机字符串
	str := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	bytes := []byte(str)
	result := []byte{}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < length; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

// RandomNumber 随机生成数字字符串
func RandomNumber(l int) string {
	str := "0123456789"
	bytes := []byte(str)
	var result []byte
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

var (
	lastTimestamp string
	counter       int
	mutex         sync.Mutex
)

// SerialNumber 生成微信支付商户订单号
// 规则：前缀(2) + 时间(12) + 序列号(4)
func SerialNumber(prefix string) string {
	mutex.Lock()
	defer mutex.Unlock()

	// 获取当前时间，格式为yyyyMMddHHmmss
	current := time.Now().Format("060102150405")

	// 如果时间戳相同则自增，否则重置计数器
	if current == lastTimestamp {
		counter++
	} else {
		lastTimestamp = current
		counter = 0
	}

	// 组合成18+位订单号：前缀(2) + 时间(12) + 序列号(4)
	return prefix + current + fmt.Sprintf("%04d", counter)
}
