package util

import "testing"

func TestRound(t *testing.T) {
	tests := []struct {
		name  string
		input float64
		want  float64
	}{
		{
			name:  "正数取整",
			input: 1.234,
			want:  1.23,
		},
		{
			name:  "负数取整",
			input: -1.234,
			want:  -1.23,
		},
		{
			name:  "零值",
			input: 0.0,
			want:  0.0,
		},
		{
			name:  "精确到分",
			input: 1.236,
			want:  1.23,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := Round(tt.input)
			t.Logf("number: %v, Round() = %v, want %v", tt.input, got, tt.want)
			if got != tt.want {
				t.<PERSON><PERSON>("Round() = %v, want %v", got, tt.want)
			}
		})
	}
}
