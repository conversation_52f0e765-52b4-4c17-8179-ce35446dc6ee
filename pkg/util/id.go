package util

import (
	"github.com/google/uuid"
	"github.com/rs/xid"
	"strconv"
)

// The function "NewXID" generates a new unique identifier (XID) and returns it as a string.
func NewXID() string {
	return xid.New().String()
}

// The function "NewXID" generates a new unique identifier (XID) and returns it as a string.
func NewToken() string {
	return xid.New().String()
}

// The function generates a new UUID and panics if there is an error.
func MustNewUUID() string {
	v, err := uuid.NewRandom()
	if err != nil {
		panic(err)
	}
	return v.String()
}

func Int64ToStr(number int64) string {
	return strconv.FormatInt(number, 10)
}

func StrToInt64(numberStr string) int64 {
	number, err := strconv.ParseInt(numberStr, 10, 64)
	if err != nil {
		return 0
	}
	return number
}
