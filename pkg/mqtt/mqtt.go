package mqtt

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"os/signal"
	"ros-api-go/internal/config"
	"ros-api-go/pkg/logging"
	"sync"
	"syscall"
	"time"

	"github.com/eclipse/paho.golang/autopaho"
	"github.com/eclipse/paho.golang/paho"
	"go.uber.org/zap"
)

type Mqtt struct {
	Client  *autopaho.ConnectionManager
	handler *Handler
	ctx     context.Context
}

func New() (*Mqtt, error) {
	ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	logging.Context(ctx).Info("MQTT 链接开始", zap.String("host", config.C.MQTT.LocalHost))
	//defer cancel()
	conf := config.C.MQTT
	server := conf.LocalHost

	serverUrl, err := url.Parse(server)
	if err != nil {
		panic(err)
	}

	genericCfg := autopaho.ClientConfig{
		ServerUrls:       []*url.URL{serverUrl},
		KeepAlive:        conf.KeepAlive,
		ConnectUsername:  conf.Username,
		ConnectPassword:  []byte(conf.Password),
		ReconnectBackoff: autopaho.NewConstantBackoff(1 * time.Second),
		ConnectTimeout:   5 * time.Second,
		OnConnectError: func(err error) {
			logging.Context(ctx).Error("MQTT-CONNECT:  OnConnectError 链接失败", zap.Error(err))
			fmt.Println("MQTT-CONNECT: 尝试连接时出错: %s\n", err)
		},
		ClientConfig: paho.ClientConfig{
			OnClientError: func(err error) {
				logging.Context(ctx).Error("MQTT-CONNECT:  ClientConfig 客户端错误", zap.Error(err))
				fmt.Println("MQTT-CONNECT: OnClientError: %s\n", err)
			},
			OnServerDisconnect: func(d *paho.Disconnect) {
				if d.Properties != nil {
					logging.Context(ctx).Error("MQTT-CONNECT: OnServerDisconnect 服务器断开连接", zap.String("reason", d.Properties.ReasonString))
					fmt.Println("MQTT-CONNECT: OnServerDisconnect 1\n")
				} else {
					logging.Context(ctx).Error("MQTT-CONNECT: OnServerDisconnect 服务器断开连接", zap.Int("reasonCode", int(d.ReasonCode)))
					fmt.Println("MQTT-CONNECT: OnServerDisconnect 2\n")
				}
			},
		},
	}
	cliCfg := genericCfg
	cliCfg.ClientID = conf.ClientID

	initialSubscriptionMade := make(chan struct{}) // 订阅成功后关闭此通道
	var initialSubscriptionOnce sync.Once          // 确保只关闭一次

	cliCfg.OnConnectionUp = func(cm *autopaho.ConnectionManager, connAck *paho.Connack) {
		logging.Context(ctx).Info("MQTT 链接成功", zap.String("host", conf.LocalHost))
		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(5*time.Second))
		defer cancel()
		if _, err := cm.Subscribe(ctx, &paho.Subscribe{
			Subscriptions: []paho.SubscribeOptions{
				{Topic: fmt.Sprintf("rpc/responses/%s", cliCfg.ClientID), QoS: 0},
			},
		}); err != nil {
			logging.Context(ctx).Error("MQTT 请求者订阅失败, 这可能导致无法接收消息", zap.Error(err))
			return
		}
		initialSubscriptionOnce.Do(func() { close(initialSubscriptionMade) })
	}

	router := paho.NewStandardRouter()
	cliCfg.OnPublishReceived = []func(paho.PublishReceived) (bool, error){
		func(p paho.PublishReceived) (bool, error) {
			router.Route(p.Packet.Packet())
			return false, nil
		}}

	cm, err := autopaho.NewConnection(ctx, cliCfg)
	if err != nil {
		return nil, err
	}

	// 等待订阅完成（否则可能会错过响应！）
	connCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	select {
	case <-connCtx.Done():
		if err := connCtx.Err(); err != nil {
			logging.Context(ctx).Error("MQTT 请求者连接和订阅失败", zap.Error(err))
			return nil, err
		}
	case <-initialSubscriptionMade:
	}

	h, err := NewHandler(ctx, HandlerOpts{
		Conn:             cm,
		Router:           router,
		ResponseTopicFmt: "rpc/response/%s",
		ClientID:         cliCfg.ClientID,
	})
	if err != nil {
		return nil, err
	}
	return &Mqtt{Client: cm, handler: h, ctx: ctx}, nil
}

// Request 向指定的商户和客户端ID发送MQTT请求，并接收响应。
// 该方法构造了一个RPC请求主题，将负载数据发送到该主题，并等待响应。
// 参数:
//   - merchantNo: 商户编号，用于构造请求主题。
//   - clientId: 客户端ID，用于构造请求主题。
//   - payload: 请求的负载数据。
//
// 返回值:
//   - []byte: 响应的负载数据。
//   - error: 如果请求失败或解析响应时发生错误，返回该错误。
func (m *Mqtt) Request(merchantNo string, clientId string, payload []byte) ([]byte, error) {
	// 构造请求主题
	rTopic := fmt.Sprintf("rpc/%s/%s/request", merchantNo, clientId)
	timeout := config.C.MQTT.RequestTimeOut * time.Second

	// 重试配置
	maxRetries := 3
	baseDelay := 1 * time.Second
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		connCtx, cancel := context.WithTimeout(context.Background(), timeout)

		resp, err := m.handler.Request(connCtx, merchantNo, clientId, &paho.Publish{
			Topic:   rTopic,
			Payload: payload,
		})

		cancel() // 确保清理资源

		if err == nil {
			return resp.Payload, nil
		}

		lastErr = err
		logging.Context(m.ctx).Error("MQTT请求失败",
			zap.Error(err),
			zap.Int("attempt", attempt+1),
			zap.Int("max_retries", maxRetries))

		// 如果不是最后一次尝试，则等待后重试
		if attempt < maxRetries-1 {
			// 使用指数退避策略
			delay := baseDelay * time.Duration(1<<uint(attempt))
			logging.Context(m.ctx).Info("等待后重试",
				zap.Duration("delay", delay),
				zap.Int("next_attempt", attempt+2))

			select {
			case <-time.After(delay):
				// 继续下一次尝试
			case <-m.ctx.Done():
				return nil, fmt.Errorf("context cancelled while retrying: %v", m.ctx.Err())
			}
		}
	}

	return nil, fmt.Errorf("after %d attempts, last error: %v", maxRetries, lastErr)
}
