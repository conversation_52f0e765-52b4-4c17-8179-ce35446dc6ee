package validation

import (
	"reflect"
	"regexp"
	"ros-api-go/pkg/validation/ug"
	"ros-api-go/pkg/validation/zh"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/ug_CN"
	zh_CN "github.com/go-playground/locales/zh_<PERSON>"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
)

var Uni *ut.UniversalTranslator

func InitValidator() {
	// 初始化验证器国际化
	initValidationLang()

	if validate, ok := binding.Validator.Engine().(*validator.Validate); ok {
		//customValidators(validate)
		validate.RegisterValidation("mobile", ValidateMobile)
	}
}

func initValidationLang() {
	// 初始化验证器
	zhLang := zh_CN.New()
	ugLang := ug_CN.New()
	Uni = ut.New(zhLang, zhLang, ugLang)
}

// func customValidators(v *validator.Validate) {
// 	// 自定义 validator
// }

// ValidateMobile 验证手机号
func ValidateMobile(fl validator.FieldLevel) bool {
	mobile := fl.Field().String()
	ok, _ := regexp.MatchString(`^1([3-9][0-9]|14[579]|5[^4]16[6]|7[1-35-8]|9[189])\d{8}$`, mobile) // 用正则去匹配
	return ok
}

func GetValidatorLocale(locale string) (ut.Translator, error) {
	var err error
	var translator ut.Translator
	if validate, ok := binding.Validator.Engine().(*validator.Validate); ok {
		switch locale {
		case "ug-CN":
			translator, _ = Uni.GetTranslator("ug_CN")
			err = ug.RegisterDefaultTranslations(validate, translator)
			break
		default:
			translator, _ = Uni.GetTranslator("zh_CN")
			err = zh.RegisterDefaultTranslations(validate, translator)
			break
		}
	}

	return translator, err
}
func GetFieldLabel(structField reflect.StructField) string {
	if label := structField.Tag.Get("label"); label != "" {
		return label
	}
	return structField.Name
}
