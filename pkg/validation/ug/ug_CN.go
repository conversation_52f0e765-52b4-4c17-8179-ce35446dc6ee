package ug

import (
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/locales"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
)

// RegisterDefaultTranslations registers a set of default translations
// for all built in tag's in validator; you may add your own as desired.
func RegisterDefaultTranslations(v *validator.Validate, trans ut.Translator) (err error) {

	translations := []struct {
		tag             string
		translation     string
		override        bool
		customRegisFunc validator.RegisterTranslationsFunc
		customTransFunc validator.TranslationFunc
	}{
		{
			tag:         "mobile",
			translation: "يانفۇن نومۇرىڭىز توغرا ئەمەس",
			override:    false,
		},
		{
			tag:         "required",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "required_if",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "required_unless",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "required_with",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "required_with_all",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "required_without",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "required_without_all",
			translation: "{0} چوقۇم تولدۇرۇلۇشى كېرەك",
			override:    false,
		},
		{
			tag: "len",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("len-string", "{0} ئۇزۇنلۇقى چوقۇم {1} بولۇشى كېرەك", false); err != nil {
					return
				}

				if err = ut.AddCardinal("len-string-character", "{0} ھەرپ", locales.PluralRuleOther, false); err != nil {
					return
				}

				if err = ut.Add("len-number", "{0} {1} گە تەڭ بولۇشى كېرەك", false); err != nil {
					return
				}

				if err = ut.Add("len-items", "{0} چوقۇم {1} تۈر بولۇشى كېرەك", false); err != nil {
					return
				}

				if err = ut.AddCardinal("min-items-item", "{0}", locales.PluralRuleOne, false); err != nil {
					return
				}

				if err = ut.AddCardinal("len-items-item", "{0} تۈر", locales.PluralRuleOther, false); err != nil {
					return
				}

				return

			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string

				var digits uint64
				var kind reflect.Kind

				if idx := strings.Index(fe.Param(), "."); idx != -1 {
					digits = uint64(len(fe.Param()[idx+1:]))
				}

				f64, err := strconv.ParseFloat(fe.Param(), 64)
				if err != nil {
					goto END
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					c, err = ut.C("len-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("len-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					c, err = ut.C("len-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("len-items", fe.Field(), c)

				default:
					t, err = ut.T("len-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "min",
			customRegisFunc: func(ut ut.Translator) (err error) {
				if err = ut.Add("min-string", "{0} ئۇزۇنلۇقى ئەڭ ئاز {1} بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.AddCardinal("min-string-character", "{0} ھەرپ", locales.PluralRuleOther, false); err != nil {
					return
				}
				if err = ut.Add("min-number", "{0} ئەڭ كىچىك قىممىتى {1} بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("min-items", "{0} ئەڭ ئاز {1} تۈر بولۇشى كېرەك", false); err != nil {
					return
				}
				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string

				var digits uint64
				var kind reflect.Kind

				if idx := strings.Index(fe.Param(), "."); idx != -1 {
					digits = uint64(len(fe.Param()[idx+1:]))
				}

				f64, err := strconv.ParseFloat(fe.Param(), 64)
				if err != nil {
					goto END
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					c, err = ut.C("min-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("min-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					c, err = ut.C("min-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("min-items", fe.Field(), c)

				default:
					t, err = ut.T("min-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "max",
			customRegisFunc: func(ut ut.Translator) (err error) {
				if err = ut.Add("max-string", "{0} ئۇزۇنلۇقى {1} دىن ئېشىپ كەتمەسلىكى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("max-number", "{0} {1} دىن چوڭ بولماسلىقى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("max-items", "{0} ئەڭ كۆپ {1} تۈر بولۇشى كېرەك", false); err != nil {
					return
				}
				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string

				var digits uint64
				var kind reflect.Kind

				if idx := strings.Index(fe.Param(), "."); idx != -1 {
					digits = uint64(len(fe.Param()[idx+1:]))
				}

				f64, err := strconv.ParseFloat(fe.Param(), 64)
				if err != nil {
					goto END
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					c, err = ut.C("max-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("max-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					c, err = ut.C("max-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("max-items", fe.Field(), c)

				default:
					t, err = ut.T("max-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag:         "eq",
			translation: "{0} {1} گە تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ne",
			translation: "{0} {1} گە تەڭ بولماسلىقى كېرەك",
			override:    false,
		},
		{
			tag: "lt",
			customRegisFunc: func(ut ut.Translator) (err error) {
				if err = ut.Add("lt-string", "{0} ئۇزۇنلۇقى {1} دىن قىسقا بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("lt-number", "{0} {1} دىن كىچىك بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("lt-items", "{0} {1} تۈردىن ئاز بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("lt-datetime", "{0} ھازىرقى ۋاقىتتىن بۇرۇن بولۇشى كېرەك", false); err != nil {
					return
				}
				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("lt-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("lt-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("lt-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("lt-items", fe.Field(), c)

				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s'不能用于struct类��.", fe.Tag())
					} else {
						t, err = ut.T("lt-datetime", fe.Field())
					}

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("lt-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "lte",
			customRegisFunc: func(ut ut.Translator) (err error) {
				if err = ut.Add("lte-string", "{0} ئۇزۇنلۇقى {1} دىن ئېشىپ كەتمەسلىكى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("lte-number", "{0} {1} دىن كىچىك ياكى تەڭ بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("lte-items", "{0} ئەڭ كۆپ {1} تۈر بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("lte-datetime", "{0} ھازىرقى ۋاقىتتىن كېيىن بولماسلىقى كېرەك", false); err != nil {
					return
				}
				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("lte-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("lte-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("lte-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("lte-items", fe.Field(), c)

				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s'不能用于struct类型.", fe.Tag())
					} else {
						t, err = ut.T("lte-datetime", fe.Field())
					}

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("lte-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "gt",
			customRegisFunc: func(ut ut.Translator) (err error) {
				if err = ut.Add("gt-string", "{0} ئۇزۇنلۇقى {1} دىن ئۇزۇن بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("gt-number", "{0} {1} دىن چوڭ بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("gt-items", "{0} {1} تۈردىن كۆپ بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("gt-datetime", "{0} ھازىرقى ۋاقىتتىن كېيىن بولۇشى كېرەك", false); err != nil {
					return
				}
				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("gt-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("gt-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("gt-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("gt-items", fe.Field(), c)

				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s'不能用于struct类型.", fe.Tag())
					} else {

						t, err = ut.T("gt-datetime", fe.Field())
					}

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("gt-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "gte",
			customRegisFunc: func(ut ut.Translator) (err error) {
				if err = ut.Add("gte-string", "{0} ئۇزۇنلۇق�� ئەڭ ئاز {1} بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("gte-number", "{0} {1} دىن چوڭ ياكى تەڭ بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("gte-items", "{0} ئەڭ ئاز {1} تۈر بولۇشى كېرەك", false); err != nil {
					return
				}
				if err = ut.Add("gte-datetime", "{0} ھازىرقى ۋاقىتتىن كېيىن ياكى تەڭ بولۇشى كېرەك", false); err != nil {
					return
				}
				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:

					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("gte-string-character", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("gte-string", fe.Field(), c)

				case reflect.Slice, reflect.Map, reflect.Array:
					var c string

					err = fn()
					if err != nil {
						goto END
					}

					c, err = ut.C("gte-items-item", f64, digits, ut.FmtNumber(f64, digits))
					if err != nil {
						goto END
					}

					t, err = ut.T("gte-items", fe.Field(), c)

				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s'不能用于struct类型.", fe.Tag())
					} else {
						t, err = ut.T("gte-datetime", fe.Field())
					}

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("gte-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("警告: 翻译字段错误: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag:         "eqfield",
			translation: "{0} {1} گە تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "eqcsfield",
			translation: "{0} {1} گە تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "necsfield",
			translation: "{0} {1} گە تەڭ بولماسلىقى كېرەك",
			override:    false,
		},
		{
			tag:         "gtcsfield",
			translation: "{0} {1} دىن چوڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "gtecsfield",
			translation: "{0} {1} دىن چوڭ ياكى تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ltcsfield",
			translation: "{0} {1} دىن كىچىك بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ltecsfield",
			translation: "{0} {1} دىن كىچىك ياكى تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "nefield",
			translation: "{0} {1} گە تەڭ بولماسلىقى كېرەك",
			override:    false,
		},
		{
			tag:         "gtfield",
			translation: "{0} {1} دىن چوڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "gtefield",
			translation: "{0} {1} دىن چوڭ ياكى تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ltfield",
			translation: "{0} {1} دىن كىچىك بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ltefield",
			translation: "{0} {1} دىن كىچىك ياكى تەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "alpha",
			translation: "{0} پەقەت ھەرپلەردىن تۈزۈلۈشى كېرەك",
			override:    false,
		},
		{
			tag:         "alphanum",
			translation: "{0} پەقەت ھەرپ ۋە سانلاردىن تۈزۈلۈشى كېرەك",
			override:    false,
		},
		{
			tag:         "alphanumunicode",
			translation: "{0} پەقەت ھەرپ ۋە يۇنىكود ھەرپلىرىدىن تۈزۈلۈشى كېرەك",
			override:    false,
		},
		{
			tag:         "alphaunicode",
			translation: "{0} پەقەت ھەرپ، سان ۋە يۇنىكود ھەرپلىرىدىن تۈزۈلۈشى كېرەك",
			override:    false,
		},
		{
			tag:         "numeric",
			translation: "{0} ئىناۋەتلىك سان بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "number",
			translation: "{0} ئىناۋەتلىك سان بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "hexadecimal",
			translation: "{0} ئىناۋەتلىك ئون ئالتىلىك سان بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "hexcolor",
			translation: "{0} ئىناۋەتلىك ئون ئالتىلىك رەڭ كودى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "rgb",
			translation: "{0} ئىناۋەتلىك RGB رەڭ كودى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "rgba",
			translation: "{0} ئىناۋەتلىك RGBA رەڭ كودى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "hsl",
			translation: "{0} ئىناۋەتلىك HSL رەڭ كودى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "hsla",
			translation: "{0} ئىناۋەتلىك HSLA رەڭ كودى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "email",
			translation: "{0} ئىناۋەتلىك ئېلخەت ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "url",
			translation: "{0} ئىناۋەتلىك تور ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "uri",
			translation: "{0} ئىناۋەتلىك URI بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "base64",
			translation: "{0} ئىناۋەتلىك Base64 تېكىستى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "contains",
			translation: "{0} چوقۇم '{1}' نى ئۆز ئىچىگە ئېلىشى كېرەك",
			override:    false,
		},
		{
			tag:         "containsany",
			translation: "{0} چوقۇم '{1}' دىكى بىرەر ھەرپنى ئۆز ئىچىگە ئېلىشى كېرەك",
			override:    false,
		},
		{
			tag:         "containsrune",
			translation: "{0} چوقۇم '{1}' ھەرپىنى ئۆز ئىچىگە ئېلىشى كېرەك",
			override:    false,
		},
		{
			tag:         "excludes",
			translation: "{0} '{1}' نى ئۆز ئىچىگە ئالماسلىقى كېرەك",
			override:    false,
		},
		{
			tag:         "excludesall",
			translation: "{0} '{1}' دىكى ھەرقانداق ھەرپنى ئۆز ئىچىگە ئالماسلىقى كېرەك",
			override:    false,
		},
		{
			tag:         "excludesrune",
			translation: "{0} '{1}' ھەرپىنى ئۆز ئىچىگە ئالماسلىقى كېرەك",
			override:    false,
		},
		{
			tag:         "endswith",
			translation: "{0} '{1}' بىلەن ئاخىرلىشىشى كېرەك",
			override:    false,
		},
		{
			tag:         "startswith",
			translation: "{0} '{1}' بىلەن باشلىنىشى كېرەك",
			override:    false,
		},
		{
			tag:         "isbn",
			translation: "{0} ئىناۋەتلىك ISBN نومۇرى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "isbn10",
			translation: "{0} ئىناۋەتلىك ISBN-10 نومۇرى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "isbn13",
			translation: "{0} ئىناۋەتلىك ISBN-13 نومۇرى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "uuid",
			translation: "{0} ئىناۋەتلىك UUID بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "uuid3",
			translation: "{0} ئىناۋەتلىك V3 UUID بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "uuid4",
			translation: "{0} ئىناۋەتلىك V4 UUID بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "uuid5",
			translation: "{0} ئىناۋەتلىك V5 UUID بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ulid",
			translation: "{0} ئىناۋەتلىك ULID بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ascii",
			translation: "{0} پەقەت ASCII ھەرپلىرىدىن تۈزۈلۈشى كېرەك",
			override:    false,
		},
		{
			tag:         "printascii",
			translation: "{0} پەقەت بېسىلىدىغان ASCII ھەرپلىرىدىن تۈزۈلۈشى كېرەك",
			override:    false,
		},
		{
			tag:         "multibyte",
			translation: "{0} كۆپ بايتلىق ھەرپلەرنى ئۆز ئىچىگە ئېلىشى كېرەك",
			override:    false,
		},
		{
			tag:         "datauri",
			translation: "{0} ئىناۋەتلىك سانلىق مەلۇمات URI سى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "latitude",
			translation: "{0} ئىناۋەتلىك كەڭلىك كوئوردىناتى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "longitude",
			translation: "{0} ئىناۋەتلىك ئۇزۇنلۇق كوئوردىناتى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ssn",
			translation: "{0} ئىناۋەتلىك ئىجتىمائىي كاپالەت نومۇرى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ipv4",
			translation: "{0} ئىناۋەتلىك IPv4 ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ipv6",
			translation: "{0} ئىناۋەتلىك IPv6 ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ip",
			translation: "{0} ئىناۋەتلىك IP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "cidr",
			translation: "{0} ئىناۋەتلىك CIDR ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "cidrv4",
			translation: "{0} ئىناۋەتلىك IPv4 CIDR ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "cidrv6",
			translation: "{0} ئىناۋەتلىك IPv6 CIDR ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "tcp_addr",
			translation: "{0} ئىناۋەتلىك TCP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "tcp4_addr",
			translation: "{0} ئىناۋەتلىك IPv4 TCP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "tcp6_addr",
			translation: "{0} ئىناۋەتلىك IPv6 TCP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "udp_addr",
			translation: "{0} ئىناۋەتلىك UDP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "udp4_addr",
			translation: "{0} ئىناۋەتلىك IPv4 UDP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "udp6_addr",
			translation: "{0} ئىناۋەتلىك IPv6 UDP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ip_addr",
			translation: "{0} ئىناۋەتلىك IP ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ip4_addr",
			translation: "{0} ئىناۋەتلىك IPv4 ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "ip6_addr",
			translation: "{0} ئىناۋەتلىك IPv6 ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "unix_addr",
			translation: "{0} ئىناۋەتلىك UNIX ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "mac",
			translation: "{0} ئىناۋەتلىك MAC ئادرېسى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "iscolor",
			translation: "{0} ئىناۋەتلىك رەڭ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "oneof",
			translation: "{0} چوقۇم [{1}] نىڭ ئىچىدىكى بىرى بولۇشى كېرەك",
			override:    false,
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {
				s, err := ut.T(fe.Tag(), fe.Field(), fe.Param())
				if err != nil {
					log.Printf("警告: 翻译字段错误: %#v", fe)
					return fe.(error).Error()
				}
				return s
			},
		},
		{
			tag:         "json",
			translation: "{0} ئىناۋەتلىك JSON تېكىستى بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "lowercase",
			translation: "{0} كىچىك ھەرپ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "uppercase",
			translation: "{0} چوڭ ھەرپ بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "datetime",
			translation: "{0} نىڭ فورماتى چوقۇم {1} بولۇشى كېرەك",
			override:    false,
		},
		{
			tag:         "PrinterNameUgExists",
			translation: "بۇ پىرىنتېرنىڭ ئۇيغۇرچە ئىسمى مەۋجۇت",
			override:    false,
		},
		{
			tag:         "PrinterNameZhExists",
			translation: "بۇ پىرىنتېرنىڭ خەنزۇچە ئىسمى مەۋجۇت",
			override:    false,
		},
	}

	for _, t := range translations {

		if t.customTransFunc != nil && t.customRegisFunc != nil {

			err = v.RegisterTranslation(t.tag, trans, t.customRegisFunc, t.customTransFunc)

		} else if t.customTransFunc != nil && t.customRegisFunc == nil {

			err = v.RegisterTranslation(t.tag, trans, registrationFunc(t.tag, t.translation, t.override), t.customTransFunc)

		} else if t.customTransFunc == nil && t.customRegisFunc != nil {

			err = v.RegisterTranslation(t.tag, trans, t.customRegisFunc, translateFunc)

		} else {
			err = v.RegisterTranslation(t.tag, trans, registrationFunc(t.tag, t.translation, t.override), translateFunc)
		}

		if err != nil {
			return
		}
	}

	return
}

func registrationFunc(tag string, translation string, override bool) validator.RegisterTranslationsFunc {

	return func(ut ut.Translator) (err error) {

		if err = ut.Add(tag, translation, override); err != nil {
			return
		}

		return

	}

}

func translateFunc(ut ut.Translator, fe validator.FieldError) string {

	log.Printf("validation trans ====> tag:%v // field: %v", fe.Tag(), fe.Field())
	t, err := ut.T(fe.Tag(), fe.Field())
	if err != nil {
		log.Printf("警告: 翻译字段错误: %#v", fe)
		return fe.(error).Error()
	}

	return t
}
