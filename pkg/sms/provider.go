package sms

import (
	"ros-api-go/internal/config"
)

// Provider 短信服务商接口
type Provider interface {
	// Initialize 初始化短信服务商配置
	Initialize(config config.SmsProviderConfig) error

	// SendSMS 发送短信
	SendSMS(request *SendRequest) error

	Templates() config.SmsTemplates
}

// SendRequest 发送短信请求
type SendRequest struct {
	PhoneNumbers  []string          // 支持批量发送
	TemplateID    string            // 模板ID
	TemplateParam map[string]string // 模板参数
}

const (
	ProviderAliyun  config.SmsProviderType = "aliyun"
	ProviderZhuTong config.SmsProviderType = "zhutong"
)
