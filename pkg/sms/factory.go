package sms

import (
	"fmt"
	"ros-api-go/internal/config"
	"sync"
)

// Factory 短信服务工厂
type Factory struct {
	providers map[config.SmsProviderType]Provider
	mu        sync.RWMutex
}

var (
	factory *Factory
	once    sync.Once
)

// GetFactory 获取短信服务工厂单例
func GetFactory() *Factory {
	once.Do(func() {
		factory = &Factory{
			providers: make(map[config.SmsProviderType]Provider),
		}
	})
	return factory
}

// Register 注册短信服务提供商
func (f *Factory) Register(providerType config.SmsProviderType, provider Provider) {
	f.mu.Lock()
	defer f.mu.Unlock()
	f.providers[providerType] = provider
}

// GetProvider 获取短信服务提供商实例
func (f *Factory) GetProvider(providerType config.SmsProviderType) (Provider, error) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	provider, exists := f.providers[providerType]
	if !exists {
		return nil, fmt.Errorf("sms provider %s not registered", providerType)
	}
	return provider, nil
}
