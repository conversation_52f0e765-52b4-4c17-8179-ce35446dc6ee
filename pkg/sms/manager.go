package sms

import (
	"github.com/redis/go-redis/v9"
	"ros-api-go/internal/config"
	"sync"
)

// Manager 短信服务管理器
type Manager struct {
	defaultProvider Provider
	mu              sync.RWMutex
	Templates       config.SmsTemplates
	RedisClient     *redis.Client
}

// NewManager 创建短信服务管理器
func NewManager(defaultProviderType config.SmsProviderType, redisClient *redis.Client) (*Manager, error) {
	manager := &Manager{
		RedisClient: redisClient,
	}

	// 获取默认provider
	provider, err := GetFactory().GetProvider(defaultProviderType)
	if err != nil {
		return nil, err
	}

	manager.defaultProvider = provider
	manager.Templates = provider.Templates()
	return manager, nil
}

// SetDefaultProvider 设置默认短信服务商
func (m *Manager) SetDefaultProvider(provider Provider) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.defaultProvider = provider
}

// SendSMS 使用默认服务商发送短信
func (m *Manager) SendSMS(request *SendRequest) error {
	m.mu.RLock()
	provider := m.defaultProvider
	m.mu.RUnlock()

	return provider.SendSMS(request)
}

// SendSMSWithProvider 使用指定服务商发送短信
func (m *Manager) SendSMSWithProvider(providerType config.SmsProviderType, request *SendRequest) error {
	provider, err := GetFactory().GetProvider(providerType)
	if err != nil {
		return err
	}

	return provider.SendSMS(request)
}
