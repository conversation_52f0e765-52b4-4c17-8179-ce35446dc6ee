package aliyun

import (
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v4/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"ros-api-go/internal/config"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/sms"
	"strings"
)

type AliyunSmsProvider struct {
	config config.SmsProviderConfig
	client *dysmsapi20170525.Client
}

func (p *AliyunSmsProvider) Templates() config.SmsTemplates {
	return p.config.Templates
}

// NewProvider 创建阿里云短信服务提供商实例
func NewProvider() sms.Provider {
	return &AliyunSmsProvider{}
}

func (p *AliyunSmsProvider) Initialize(config config.SmsProviderConfig) error {
	p.config = config

	apiConfig := &openapi.Config{
		AccessKeyId:     tea.String(config.AccessKeyID),
		AccessKeySecret: tea.String(config.AccessKeySecret),
		Endpoint:        tea.String(config.EndPoint),
	}

	client, err := dysmsapi20170525.NewClient(apiConfig)
	if err != nil {
		return err
	}

	p.client = client
	return nil
}

// SendSMS 发送短信
func (p *AliyunSmsProvider) SendSMS(request *sms.SendRequest) error {
	templateParamsStr, err := json.Marshal(request.TemplateParam)
	if err != nil {
		return err
	}

	sendRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(strings.Join(request.PhoneNumbers, ",")),
		SignName:      tea.String(p.config.SignName),
		TemplateCode:  tea.String(request.TemplateID),
		TemplateParam: tea.String(string(templateParamsStr)),
	}

	_, err = p.client.SendSmsWithOptions(sendRequest, &util.RuntimeOptions{})
	return err
}
