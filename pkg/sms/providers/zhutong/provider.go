package zhutong

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"ros-api-go/internal/config"
	"ros-api-go/pkg/errors"
	pkgsms "ros-api-go/pkg/sms"
	"strconv"
	"time"
)

const (
	apiEndpoint = "https://api-shss.zthysms.com/v2/sendSmsTp"
)

type ZhuTongProvider struct {
	config config.SmsProviderConfig
}

// NewProvider 创建助通短信服务提供商实例
func NewProvider() pkgsms.Provider {
	return &ZhuTongProvider{}
}

// Initialize 初始化助通短信服务提供商配置
func (p *ZhuTongProvider) Initialize(config config.SmsProviderConfig) error {
	p.config = config
	return nil
}

// Templates 返回短信模板列表
func (p *ZhuTongProvider) Templates() config.SmsTemplates {
	return p.config.Templates
}

// SendSMS 发送短信
func (p *<PERSON>TongProvider) SendSMS(request *pkgsms.SendRequest) error {
	// 生成时间戳
	tKey := fmt.Sprintf("%d", time.Now().Unix())

	// 构建请求体
	reqBody := &SendSmsRequest{
		Username:  p.config.AccessKeyID,
		Password:  p.generatePassword(tKey),
		TKey:      tKey,
		TpID:      request.TemplateID,
		Signature: fmt.Sprintf("【%s】", p.config.SignName),
		Records:   make([]Record, 0, len(request.PhoneNumbers)),
	}

	// 添加手机号和模板参数
	for _, phone := range request.PhoneNumbers {
		record := Record{
			Mobile:    phone,
			TpContent: request.TemplateParam,
		}
		reqBody.Records = append(reqBody.Records, record)
	}

	// 发送请求
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %w", err)
	}

	// 创建 HTTP 客户端
	//client := &http.Client{
	//	Timeout: time.Second * 10, // 设置超时时间
	//}

	// 如果启用了代理，设置代理
	//proxyURL, err := url.Parse("http://127.0.0.1:10809")
	//if err != nil {
	//	return fmt.Errorf("parse proxy url failed: %w", err)
	//}

	// 创建 SOCKS5 代理拨号器
	//dialer, err := proxy.SOCKS5("tcp", "192.168.1.57:8080", nil, proxy.Direct)
	//if err != nil {
	//	return fmt.Errorf("create socks5 dialer failed: %w", err)
	//}
	//
	//// 创建自定义 Transport
	//transport := &http.Transport{
	//	DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
	//		return dialer.Dial(network, addr)
	//	},
	//}
	//
	//client.Transport = transport

	// 创建请求
	//req, err := http.NewRequest(http.MethodPost, apiEndpoint, bytes.NewBuffer(jsonData))
	//if err != nil {
	//	return fmt.Errorf("create request failed: %w", err)
	//}
	//
	//// 设置请求头
	//req.Header.Set("Content-Type", "application/json")
	//
	//// 发送请求
	//resp, err := client.Do(req)

	resp, err := http.Post(apiEndpoint, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("send request failed: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var response SendSmsResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("decode response failed: %w", err)
	}

	// 检查响应状态
	if response.Code != 200 {
		return errors.BadRequest(strconv.Itoa(response.Code), response.Msg)
	}

	return nil
}

// generatePassword 生成双重MD5密码
// md5( md5( password ) + tKey )
func (p *ZhuTongProvider) generatePassword(tKey string) string {
	return p.enMd5(p.enMd5(p.config.AccessKeySecret) + tKey)
}

// enMd5 加密字符串为MD5字符串
func (p *ZhuTongProvider) enMd5(mstr string) string {

	data := []byte(mstr)
	md5obj := md5.New()
	md5obj.Write(data)
	passowrd := md5obj.Sum(nil)
	return hex.EncodeToString(passowrd)

}
