package zhutong

// SendSmsRequest 发送短信请求
type SendSmsRequest struct {
	Username  string   `json:"username"`
	Password  string   `json:"password"`
	TKey      string   `json:"tKey"`
	TpID      string   `json:"tpId"`
	Signature string   `json:"signature"`
	Ext       string   `json:"ext,omitempty"`
	Extend    string   `json:"extend,omitempty"`
	Records   []Record `json:"records"`
}

// Record 短信发送记录
type Record struct {
	Mobile    string            `json:"mobile"`
	TpContent map[string]string `json:"tpContent,omitempty"`
}

// SendSmsResponse 发送短信响应
type SendSmsResponse struct {
	Code        int      `json:"code"`
	Msg         string   `json:"msg"`
	TpID        string   `json:"tpId"`
	MsgID       string   `json:"msgId"`
	InvalidList []Record `json:"invalidList,omitempty"`
}

// ErrorCode 错误码定义
var ErrorCode = map[int]string{
	4001: "用户名错误",
	4002: "密码不能为空",
	4004: "手机号码错误",
	4006: "IP鉴权错误",
	4007: "用户禁用",
	4008: "tKey错误",
	4009: "密码错误",
	4013: "定时时间错误",
	4014: "模板错误",
	4015: "扩展号错误",
	4019: "用户类型错误",
	4023: "签名错误",
	4025: "模板变量内容为空",
	4026: "手机号码数最大2000个",
	4027: "模板变量内容最大200组",
	4029: "请使用post请求",
	4030: "Content-Type请使用application/json",
	9998: "JSON解析错误",
	9999: "非法请求",
}
