package websocket

import (
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"log"
	"net/http"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/jwtx"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"sync"
	"time"
)

// 定义WebSocket相关常量
const (
	readBufferSize  = 1024                // 读缓冲区大小
	writeBufferSize = 1024                // 写缓冲区大小
	writeWait       = 10 * time.Second    // 写操作超时时间
	pongWait        = 5 * time.Second     // pong消息等待时间
	pingPeriod      = (pongWait * 9) / 10 // ping消息发送间隔
)

// WebSocket升级器配置
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool { // 允许所有来源连接
		return true
	},
	ReadBufferSize:  readBufferSize,
	WriteBufferSize: writeBufferSize,
}

// Server WebSocket服务器结构体
type Server struct {
	clients    map[string]map[*clientConn]bool // 按merchantNo分组的客户端连接映射
	clientsMux sync.RWMutex                    // 客户端映射的读写锁
	db         *gorm.DB                        // 数据库连接
	auth       jwtx.Auther                     // JWT验证器
}

// 单例模式相关变量
var (
	serverInstance *Server   // 服务器实例
	once           sync.Once // 保证单例
)

// WsServer 创建WebSocket服务器实例
func WsServer(db *gorm.DB, auth jwtx.Auther) *Server {
	once.Do(func() {
		serverInstance = &Server{
			clients: make(map[string]map[*clientConn]bool),
			auth:    auth,
			db:      db,
		}
	})
	return serverInstance
}

type AuthData struct {
	MerchantNo string `json:"merchantNo"`
	Token      string `json:"token"`
}

// HandleConnections 处理新连接
func (s *Server) HandleConnections(ctx *gin.Context) {
	// 升级HTTP连接到WebSocket
	ws, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		log.Println("WebsSocket Upgrade error:", err)
		ws.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseInternalServerErr, "Upgrade failed"))
		ws.Close()
		return
	}

	// 创建clientConn
	client := &clientConn{conn: ws}
	defer ws.Close()

	// 获取merchantNo
	_, msg, err := ws.ReadMessage()
	if err != nil {
		log.Println("Websocket读取消息失败:", err)
		err := client.safeWrite(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseProtocolError, "Read message failed"))
		if err != nil {
			log.Println("Websocket Write error:", err)
		}
		return
	}

	// 获取merchantNo和token
	authData := AuthData{}
	err = json.Unmarshal(msg, &authData)
	if err != nil {
		log.Println("Websocket解析消息失败:", err)
		err := client.safeWrite(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseInvalidFramePayloadData, "Invalid message format"))
		if err != nil {
			log.Println("Websocket Write error:", err)
		}
		return
	}

	if authData.Token == "" {
		log.Println("Websocket Missing merchantNo header")
		err := client.safeWrite(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.ClosePolicyViolation, "Missing token"))
		if err != nil {
			log.Println("Websocket Write error:", err)
		}
		return
	}

	if ok, err := s.CheckAuth(ctx, authData, client); err != nil || !ok {
		logging.Context(ctx).Error("Websocket authentication failed: Invalid token", zap.String("token", authData.Token), zap.Error(err))
		err := client.safeWrite(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.ClosePolicyViolation, "Authentication failed"))
		if err != nil {
			log.Println("Websocket Write error:", err)
		}
		return
	}

	// 设置Pong处理程序
	ws.SetPongHandler(func(string) error {
		ws.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	// 启动心跳goroutine
	done := make(chan struct{})
	go func() {
		ticker := time.NewTicker(pingPeriod)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				ws.SetWriteDeadline(time.Now().Add(writeWait))
				if err := client.safeWrite(websocket.PingMessage, nil); err != nil {
					log.Println("Websocket Ping error:", err)
					return
				}
			case <-done:
				return
			}
		}
	}()

	// 注册并注销客户端
	clientIP := util.GetClientIP(ctx.Request)
	s.registerClient(authData.MerchantNo, client, clientIP)
	defer s.unregisterClient(authData.MerchantNo, client, clientIP)
	defer close(done)

	// 主消息循环
	ws.SetReadDeadline(time.Now().Add(pongWait))
	for {
		_, message, err := ws.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Read error: %v", err)
				err := client.safeWrite(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseInternalServerErr, "Read error"))
				if err != nil {
					log.Println("Websocket Write error:", err)
				}
			}
			break
		}
		s.handleMessage(client, message)
	}
}

// 注册客户端
func (s *Server) registerClient(merchantNo string, client *clientConn, clientIP string) {
	log.Printf("Websocket Registered client: %s - %s\n", merchantNo, clientIP)
	s.clientsMux.Lock()
	defer s.clientsMux.Unlock()

	// 如果该merchantNo还没有客户端组，先创建
	if _, exists := s.clients[merchantNo]; !exists {
		s.clients[merchantNo] = make(map[*clientConn]bool)
	}

	s.clients[merchantNo][client] = true
	log.Printf("Websocket Number of clients for %s: %d\n", merchantNo, len(s.clients[merchantNo]))
}

// 注销客户端
func (s *Server) unregisterClient(merchantNo string, client *clientConn, clientIP string) {
	log.Printf("Websocket Unregistered client: %s-%d-%s\n", merchantNo, client.userID, clientIP)
	s.clientsMux.Lock()
	defer s.clientsMux.Unlock()

	if group, exists := s.clients[merchantNo]; exists {
		delete(group, client)
		// 如果该merchantNo没有客户端了，删除整个组
		if len(group) == 0 {
			delete(s.clients, merchantNo)
		}
	}
}

// 处理接收到的消息
func (s *Server) handleMessage(client *clientConn, message []byte) {
	log.Printf("Websocket Received message: %s\n", message)
	// TODO: 添加消息处理逻辑
}

// CheckAuth 检查token和merchantNO是否有效
func (s *Server) CheckAuth(c *gin.Context, data AuthData, client *clientConn) (bool, error) {

	ctx := c.Request.Context()
	ErrInvalidToken := errors.BadRequest("WebsocketAuthenticationFailed", "Invalid token")

	id, err := s.auth.ParseSubject(ctx, data.Token)
	if err != nil || id == 0 {
		log.Printf("Websocket Auth: %s", data.MerchantNo)
		return false, ErrInvalidToken
	}
	// 检查token是否存在
	ok, err := util.Exists(ctx,
		util.GetDB(ctx, s.db).Table("merchant_employees").
			Where("merchant_no = ? and user_id = ? and state = ?", data.MerchantNo, id, 1))
	if err != nil || !ok {
		log.Printf("Websocket Auth: 商户信息查询失败：%s, %d \n", data.MerchantNo, id)
		return false, ErrInvalidToken
	}
	// 连接中保存用户ID
	client.userID = id

	return true, nil
}
