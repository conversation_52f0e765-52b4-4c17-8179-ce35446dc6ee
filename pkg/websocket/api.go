package websocket

import (
	"github.com/gorilla/websocket"
	"log"
	"ros-api-go/internal/config"
	"ros-api-go/pkg/encoding/json"
	"sync"
)

// Message 消息结构体
type Message struct {
	ID        string `json:"id"`        // 消息唯一标识
	Data      any    `json:"data"`      // 消息内容（任意类型）
	Timestamp int64  `json:"timestamp"` // 发送时间戳
}

// clientConn 包装了 websocket.Conn，添加了写入锁
type clientConn struct {
	conn     *websocket.Conn
	userID   int64
	writeMux sync.Mutex // 保护写入操作的互斥锁
}

// safeWrite 安全地向WebSocket连接写入消息
func (c *clientConn) safeWrite(messageType int, data []byte) error {
	c.writeMux.Lock()
	defer c.writeMux.Unlock()
	return c.conn.WriteMessage(messageType, data)
}

func GetServerUrl() string {
	siteUrl := config.C.General.SiteUrl
	// 根据siteUrl生成WebSocket连接地址
	if siteUrl == "" {
		return ""
	}
	// 替换协议并返回WebSocket地址
	wsUrl := "ws" + siteUrl[4:] + config.C.General.WebsocketRoute
	return wsUrl
}

func NumClients() int {
	return len(serverInstance.clients)
}

// newMessage 将任意消息转换为WebSocket消息格式
func newMessage(message any) []byte {
	// 将消息序列化为JSON
	msgStr, err := json.Marshal(message)
	if err != nil {
		log.Println("Websocket Marshal error:", err)
		return nil
	}
	return msgStr
}

// Broadcast 向所有客户端广播消息
func Broadcast(merchantNo string, message any) {
	// 转换消息格式
	msgStr := newMessage(message)
	if msgStr == nil {
		return
	}

	// 异步处理广播消息
	go func() {
		serverInstance.clientsMux.Lock()
		clients := serverInstance.clients[merchantNo]
		if clients == nil {
			serverInstance.clientsMux.Unlock()
			log.Println("No clients found for merchantNo:", merchantNo)
			return
		}

		// 创建当前客户端的快照，避免长时间持有全局锁
		activeClients := make([]*clientConn, 0, len(clients))
		for client := range clients {
			activeClients = append(activeClients, client)
		}
		serverInstance.clientsMux.Unlock()

		// 使用WaitGroup等待所有发送操作完成
		var wg sync.WaitGroup
		for _, client := range activeClients {
			wg.Add(1)
			go func(c *clientConn) {
				defer wg.Done()

				// 发送消息
				if err := c.safeWrite(websocket.TextMessage, msgStr); err != nil {
					log.Println("Websocket Write error:", err)

					// 处理发送失败的情况
					serverInstance.clientsMux.Lock()
					if clients := serverInstance.clients[merchantNo]; clients != nil {
						delete(clients, c)
					}
					serverInstance.clientsMux.Unlock()

					if err := c.conn.Close(); err != nil {
						log.Println("Websocket Close error:", err)
					}
				}
			}(client)
		}
		wg.Wait() // 等待所有发送操作完成
	}()
}

// SendToClient 向指定客户端发送消息
func SendToClient(merchantNo string, client *clientConn, message any) error {
	// 转换消息格式
	msgStr := newMessage(message)
	if msgStr == nil {
		return nil
	}

	serverInstance.clientsMux.Lock()
	clients := serverInstance.clients[merchantNo]
	if clients == nil {
		serverInstance.clientsMux.Unlock()
		log.Println("No clients found for merchantNo:", merchantNo)
		return nil
	}

	// 检查客户端是否在线
	if _, ok := clients[client]; !ok {
		serverInstance.clientsMux.Unlock()
		return nil
	}
	serverInstance.clientsMux.Unlock()

	// 安全发送消息
	return client.safeWrite(websocket.TextMessage, msgStr)
}
