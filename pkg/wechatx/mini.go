package wechatx

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"github.com/go-pay/gopay/pkg/xhttp"
	"go.uber.org/zap"
	"ros-api-go/internal/config"
	"ros-api-go/pkg/encoding/json"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
)

// Code2SessionResp 微信小程序code换openid返回结果
type Code2SessionResp struct {
	Errcode int64  `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Openid  string `json:"openid"`
	Session string `json:"session_key"`
	Unionid string `json:"unionid"`
}

// CodeToSession 调用jscode2session获取openid
func CodeToSession(ctx context.Context, authCode string) (*Code2SessionResp, error) {
	var resp Code2SessionResp
	url := fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		config.C.WechatMini.AppID, config.C.WechatMini.AppSecret, authCode)
	logging.Context(ctx).Info("jscode2session url", zap.String("URL", url))
	result, err := xhttp.NewClient().Req().Get(url).EndStruct(ctx, &resp)

	if err != nil {
		return nil, err
	}

	if resp.Errcode != 0 {
		logging.Context(ctx).Error("微信授权失败", zap.String("URL", url), zap.Any("result", result), zap.Any("resp", resp))
		return nil, errors.BadRequest("", "微信授权失败: %v", resp.Errmsg)
	}
	return &resp, nil
}

// CodeToOpenId 调用jscode2session获取openid
func CodeToOpenId(ctx context.Context, authCode string) (openid string, err error) {
	session, err := CodeToSession(ctx, authCode)
	if err != nil {
		return "", err
	}
	return session.Openid, nil
}

type DecryptedPlainData struct {
	OpenID          string `json:"openId"`
	UnionID         string `json:"unionId"`
	NickName        string `json:"nickName"`
	Gender          int    `json:"gender"`
	City            string `json:"city"`
	Province        string `json:"province"`
	Country         string `json:"country"`
	AvatarURL       string `json:"avatarUrl"`
	Language        string `json:"language"`
	PhoneNumber     string `json:"phoneNumber"`
	OpenGID         string `json:"openGId"`
	MsgTicket       string `json:"msgTicket"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
	Watermark       struct {
		Timestamp int64  `json:"timestamp"`
		AppID     string `json:"appid"`
	} `json:"watermark"`
}

// DecryptData 解密微信小程序加密数据
func DecryptData(sessionKey, encrypted, iv string) (*DecryptedPlainData, error) {
	if len(sessionKey) != 24 {
		return nil, errors.BadRequest("", "sessionKey长度错误")
	}
	if len(iv) != 24 {
		return nil, errors.BadRequest("", "iv长度错误")
	}

	aesKey, err := base64.StdEncoding.DecodeString(sessionKey)
	if err != nil {
		return nil, err
	}

	ivBytes, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return nil, err
	}

	encryptedBytes, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}

	mode := cipher.NewCBCDecrypter(block, ivBytes)
	plaintext := make([]byte, len(encryptedBytes))
	mode.CryptBlocks(plaintext, encryptedBytes)

	// 去除PKCS#7填充
	padding := int(plaintext[len(plaintext)-1])
	plaintext = plaintext[:len(plaintext)-padding]
	var decrypted DecryptedPlainData
	err = json.Unmarshal(plaintext, &decrypted)
	if err != nil {
		return nil, err
	}

	return &decrypted, nil
}
