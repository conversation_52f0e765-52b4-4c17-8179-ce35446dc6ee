package wechatx

import (
	"encoding/base64"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat"
	wechatV3 "github.com/go-pay/gopay/wechat/v3"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"time"
)

const (
	QUERY_BY_TRANSACTION_ID = 1
	QUERY_BY_OUT_TRADE_NO   = 2
)

// GetPaymentNotifyUrl 获取微信支付通知地址
// paymentNo 支付订单号
func GetPaymentNotifyUrl(orderType int64, paymentNo string) string {
	if orderType == consts.ORDER_TYPE_ORDER {
		return GetOrderNotifyUrl(paymentNo)
	}
	if orderType == consts.ORDER_TYPE_DEBT_REPAYMENT {
		return GetDebtRepaymentNotifyUrl(paymentNo)
	}
	return GetRechargeNotifyUrl(paymentNo)
}

// GetOrderNotifyUrl 获取微信退款通知地址
func GetOrderNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/wechat/payment-notify/" + paymentNo
}

// GetRefundNotifyUrl 获取微信退款通知地址
func GetRefundNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/wechat/refund-notify/" + paymentNo
}

// GetRechargeNotifyUrl 获取微信充值通知地址
func GetRechargeNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/wechat/recharge-notify/" + paymentNo
}

// GetDebtRepaymentNotifyUrl 获取微信赊账还款通知地址
func GetDebtRepaymentNotifyUrl(paymentNo string) string {
	return config.C.General.SiteUrl + "/api/v2/payment/wechat/debt-repayment-notify/" + paymentNo
}

// ParseWechatTime 解析微信时间字符串 (V3 版本API)
func ParseWechatTime(wechatTime string) (time.Time, error) {
	return time.Parse(time.RFC3339, wechatTime)
}

// ParseWechatTimeV2 解析微信时间字符串 (V2 版本API)
func ParseWechatTimeV2(wechatTime string) (time.Time, error) {
	// 北京时间
	location, _ := time.LoadLocation("Asia/Shanghai")
	return time.ParseInLocation("20060102150405", wechatTime, location)
}

// NewWechatPayClient 新建微信支付客户端 (V2 版本API)
func NewWechatPayClient() (*wechat.Client, error) {

	wechatConfig := config.C.WechatPayment

	appid := wechatConfig.AppID
	mchid := wechatConfig.MchID
	key := wechatConfig.MchApiKey

	client := wechat.NewClient(appid, mchid, key, true)

	if config.C.IsDebug() {
		client.DebugSwitch = gopay.DebugOn
	}

	apiCert, err := base64.StdEncoding.DecodeString(wechatConfig.ApiClientCert)
	if err != nil {
		return nil, err
	}
	apiKey, err := base64.StdEncoding.DecodeString(wechatConfig.ApiClientKey)
	if err != nil {
		return nil, err
	}
	err = client.AddCertPemFileContent(apiCert, apiKey)
	if err != nil {
		return nil, err
	}

	return client, nil
}

// Verified 验证微信支付回调签名 (V2 版本API)
func Verified(wxRsp interface{}) (bool, error) {
	return wechat.VerifySign(config.C.WechatPayment.MchApiKey, wechat.SignType_MD5, wxRsp)
}

// NewWechatPayClientV3 新建微信支付客户端 (V3 版本API)
func NewWechatPayClientV3() (*wechatV3.ClientV3, error) {

	wechatConfig := config.C.WechatPayment

	mchid := wechatConfig.MchID
	apiKeyV3 := wechatConfig.MchApiV3Key
	serialNo := wechatConfig.WechatPaySerial
	apiKey, err := base64.StdEncoding.DecodeString(wechatConfig.ApiClientKey)
	if err != nil {
		return nil, err
	}
	client, err := wechatV3.NewClientV3(mchid, serialNo, apiKeyV3, string(apiKey))

	if err != nil {
		return nil, err
	}

	if config.C.IsDebug() {
		client.DebugSwitch = gopay.DebugOn
	}

	// 自动更新证书
	err = client.AutoVerifySign()
	if err != nil {
		return nil, err
	}

	return client, nil
}
